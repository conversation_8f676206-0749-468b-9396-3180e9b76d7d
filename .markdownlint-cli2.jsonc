{"customRules": ["markdownlint-rule-enhanced-proper-names/src/enhanced-proper-names.js", "markdownlint-rule-no-trailing-slash-in-links/src/no-trailing-slash-in-links.js"], "config": {"default": true, "first-heading-h1": false, "heading-style": {"style": "atx"}, "ul-style": false, "no-hard-tabs": {"spaces_per_tab": 4}, "line-length": false, "no-duplicate-heading": {"allow_different_nesting": true}, "no-inline-html": {"allowed_elements": ["Badge", "TabItem", "Tabs", "a", "br", "div", "img", "li", "ul", "kbd", "p", "span", "sup", "iframe", "VersionAdd"]}, "fenced-code-language": false, "first-line-heading": false, "no-alt-text": true, "code-block-style": {"style": "fenced"}, "code-fence-style": {"style": "backtick"}, "link-fragments": false, "no-trailing-slash-in-links": true, "enhanced-proper-names": {"code_blocks": false, "html_elements": false, "heading_id": false, "names": ["ACL", "AI", "API", "ARM", "ARM64", "AWS", "Amazon", "Ansible", "Apache", "Azure", "BSD", "BadgerDB", "CDH", "CPU", "CSI Driver", "CSI", "CentOS", "<PERSON><PERSON>", "CephFS", "ClickHouse", "Cloud SQL", "<PERSON><PERSON>", "Consul", "Debian", "DevOps", "DigitalOcean", "DistCp", "<PERSON><PERSON>", "<PERSON>er", "Dockerfile", "<PERSON>", "ECI", "Elasticsearch", "FTP", "FUSE", "Flink", "Fluid", "FoundationDB", "GCC", "GID", "Git", "GitHub", "Google", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "HBase", "HDFS", "HDP", "HTTP", "HTTPS", "<PERSON><PERSON>", "Hive Metastore", "Hive", "<PERSON><PERSON>", "IAM", "ID", "IOPS", "IP", "Iceberg", "JAR", "JDK", "JSON", "Java", "JuiceFS", "JuiceFSRuntime", "Juicedata", "K3s", "K8s", "<PERSON><PERSON><PERSON>", "KeyDB", "KubeSphere", "Kubernetes", "LDAP", "LZ4", "Linux", "M1", "MariaDB", "<PERSON><PERSON>", "MinIO", "MySQL", "NFS", "NGINX", "POSIX", "PV", "PVC", "PostgreSQL", "PowerShell", "Prometheus", "Pushgateway", "Python", "QPS", "QoS", "RADOS", "RESTful", "RGW", "RPC", "Raft", "Rancher", "<PERSON>", "Redis", "S3", "S3QL", "SDK", "SFTP", "SID", "SMB", "SQL", "SQLite", "SSH", "Samba", "Scala", "Spark", "StarRocks", "ThriftServer", "TiKV", "Trino", "UID", "UUID", "Ubuntu", "Unix", "VFS", "WSL", "WebDAV", "WinFsp", "Windows", "YAML", "YARN", "Zstandard", "etcd", "macFUSE", "macOS"]}}}