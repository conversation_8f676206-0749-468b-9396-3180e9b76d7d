name: 'Build Action'
description: 'Build action'
inputs:
  target:
    description: 'build target: juicefs, juicefs.fdb etc'
    required: true
    default: 'juicefs'
  useBeta:
    description: 'use beta version of juicefs to skip build'
    required: true
    type: boolean
    default: false
runs:
  using: "composite"
  steps:
    - uses: actions/setup-go@v3
      with:
        go-version: 'oldstable'
        cache: true

    - name: Change go version for root user
      shell: bash
      run: |
        go_path=`which go`
        echo $go_path
        root_go_path=`sudo which go`
        echo $root_go_path
        sudo rm -f $root_go_path
        sudo ln -s $go_path $root_go_path
        go version
        sudo go version

    - name: Install tools
      shell: bash
      run: |
        if [ "${{inputs.target}}" == "juicefs.fdb" ]; then
          wget -q https://github.com/apple/foundationdb/releases/download/6.3.23/foundationdb-clients_6.3.23-1_amd64.deb
          sudo dpkg -i foundationdb-clients_6.3.23-1_amd64.deb
        elif [ "${{inputs.target}}" == "juicefs.gluster" ]; then
          sudo .github/scripts/apt_install.sh uuid-dev libglusterfs-dev
        fi

    - name: Build linux target
      shell: bash
      run: |
        if ${{ inputs.useBeta }}; then
          echo "try use beta version of juicefs"
          if wget -q https://juicefs-pub.oss-cn-hangzhou.aliyuncs.com/${{inputs.target}}; then
            [ "${{inputs.target}}" != "juicefs" ] && mv ${{inputs.target}} juicefs
            sudo chmod +x juicefs
            version=$(./juicefs version)
            date1=$( echo $version | grep -oP '\d{4}-\d{2}-\d{2}')
            date2=$(date +%Y-%m-%d)
            if [ "$date1" == "$date2" ]; then
              echo beta version is up to date, $version, use it to skip build
              exit 0
            else
              echo beta version is out of date, $version, remove it and build
              rm -f juicefs
            fi
          fi
        fi
        echo "start to build ${{inputs.target}}"
        make ${{inputs.target}}
        if ${{ inputs.useBeta }}; then
          echo "upload beta version"
          curl --upload-file ${{inputs.target}} https://juicefs-pub.oss-cn-hangzhou.aliyuncs.com/
          echo "upload beta version succeed"
        fi
        [ "${{inputs.target}}" != "juicefs" ] &&  mv ${{inputs.target}} juicefs
        echo "build ${{inputs.target}} succeed"