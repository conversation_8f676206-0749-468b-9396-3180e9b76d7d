name: verify

on:
  push:
    branches:
      - main
      - 'release-**'
    paths-ignore:
      - 'docs/**'
      - '**.md'
      - '.github/**'
      - '!.github/workflows/verify.yml'
  pull_request:
    branches:
      - 'main'
      - 'release-**'
    paths-ignore:
      - 'docs/**'
      - '**.md'
      - '.github/**'
      - '!.github/workflows/verify.yml'
  workflow_dispatch:

jobs:
  lint:
    runs-on: ubuntu-20.04
    steps:
      - uses: actions/setup-go@v3
        with:
          go-version: 'oldstable'

      - uses: actions/checkout@v3
      - name: golangci-lint
        uses: golangci/golangci-lint-action@v3
        with:
          version: v1.51.0
            
      - name: Setup upterm session
        if: ${{ failure() && github.event_name == 'workflow_dispatch' && github.event.inputs.debug == 'true' }}
        # if: failure()
        timeout-minutes: 60
        uses: lhotari/action-upterm@v1
  build:
    strategy:
      fail-fast: false
      matrix:
        version: ['1.18', '1.19', '1.20', '1.21']
    runs-on: ubuntu-20.04
    steps:
    - name: Check out code
      uses: actions/checkout@v3

    - name: Set up Go
      uses: actions/setup-go@v3
      with:
        go-version: ${{ matrix.version }}
        cache: true
      id: go
      
    - name: Install dependencies
      run: |
        sudo .github/scripts/apt_install.sh g++-multilib gcc-mingw-w64

    - name: Build linux target
      run: |
        make
        ./juicefs version

    - name: build lite
      run: | 
        make juicefs.lite
        ./juicefs.lite version

    - name: build windows
      run: make juicefs.exe

    - name: build libjfs.dll
      run: make -C sdk/java/libjfs libjfs.dll

    - name: build ceph
      run: |
        sudo .github/scripts/apt_install.sh librados-dev
        make juicefs.ceph
        ./juicefs.ceph version

    - name: build fdb
      run: |
        wget https://github.com/apple/foundationdb/releases/download/6.3.23/foundationdb-clients_6.3.23-1_amd64.deb
        sudo dpkg -i foundationdb-clients_6.3.23-1_amd64.deb
        make juicefs.fdb
        ./juicefs.fdb version

    - name: Setup upterm session
      if: failure() && (github.event.inputs.debug == 'true' || github.run_attempt != 1)
      # if: failure()
      timeout-minutes: 60
      uses: lhotari/action-upterm@v1