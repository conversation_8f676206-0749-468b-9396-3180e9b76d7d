name: "ltp"

on:
  push:
    branches:
      - 'release-**'
    paths-ignore:
      - 'docs/**'
  pull_request:
    #The branches below must be a subset of the branches above
    branches:
      - 'release-**'
    paths-ignore:
      - 'docs/**'
  schedule:
    - cron:  '30 20 * * *'
  workflow_dispatch:

jobs:
  ltpfs:
    runs-on: ubuntu-20.04
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 1

      - name: Build
        uses: ./.github/actions/build

      - name: Copy
        run: |
          cp .github/workflows/bash/rm_fs /tmp/
          cp .github/workflows/bash/rm_list.sh /tmp/

      - name: Run Redis
        run: |
          sudo docker run -d --name redis -v redis-data:/data  \
          -p 6379:6379  redis redis-server --appendonly yes

      - name: Juicefs Format
        run: |
          sudo ./juicefs format --trash-days 0 redis://127.0.0.1:6379/1 pics

      - name: Juicefs Mount
        run: |
          sudo ./juicefs mount -d redis://127.0.0.1:6379/1 /tmp/jfs --no-usage-report &

      - name: LTP FS
        timeout-minutes: 50
        run: |
          sudo .github/scripts/apt_install.sh libaio-dev libacl1-dev attr
          wget -O ltp.tar.gz https://github.com/linux-test-project/ltp/archive/refs/tags/20210927.tar.gz
          mkdir ltp
          tar -xvf ltp.tar.gz -C ltp --strip-components 1
          cd ltp
          ls -lh
          make autotools
          ./configure
          make
          sudo make install
          cd /opt/ltp
          sudo chmod +x /tmp/rm_list.sh
          sudo chmod 777 runtest/fs
          sudo /tmp/rm_list.sh /tmp/rm_fs /opt/ltp/runtest/fs
          sudo ./runltp -d /tmp/jfs -f fs,fs_perms_simple,fsx,io,fcntl-locktests -C result.log.failed -T result.log.tconf -l result.log

      - name: tconf Log
        if: always()
        run: |
          cat /opt/ltp/output/result.log.tconf

      - name: check ltpsyscall failed log
        if: always()
        run: |
          cat /opt/ltp/output/result.log.failed

      - name: Send Slack Notification
        if: failure()
        uses: juicedata/slack-notify-action@main
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID_FOR_PR_CHECK_NOTIFY }}"
          slack_bot_token: "${{ secrets.SLACK_BOT_TOKEN }}"  
