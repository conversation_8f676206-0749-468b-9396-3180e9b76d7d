name: "elastictest"

on:
#  push:
#    branches:
#      - 'release-**'
#    paths-ignore:
#      - 'docs/**'
#  pull_request:
#    #The branches below must be a subset of the branches above
#    branches:
#      - 'release-**'
#    paths-ignore:
#      - 'docs/**'
  schedule:
    - cron:  '30 20 * * *'

  workflow_dispatch:
    inputs:
      debug:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false

jobs:
  elastictest:
    runs-on: ubuntu-20.04
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 1

      - name: Build 
        uses: ./.github/actions/build

      - name: Run Redis
        run: |
          sudo docker run -d --name redis -v redis-data:/data  \
          -p 6379:6379  redis redis-server --appendonly yes

      - name: Setup minio
        run: |
          docker run -d -p 9000:9000 --name minio \
                    -e "MINIO_ACCESS_KEY=minioadmin" \
                    -e "MINIO_SECRET_KEY=minioadmin" \
                    -v /tmp/data:/data \
                    -v /tmp/config:/root/.minio \
                    minio/minio server /data
          sleep 5

      - name: Juicefs Format
        run: |
          sudo ./juicefs format --trash-days 0 --storage minio --bucket http://127.0.0.1:9000/mypics \
             --access-key minioadmin \
             --secret-key minioadmin \
             redis://127.0.0.1:6379/1 pics

      - name: Juicefs Mount
        run: |
          sudo ./juicefs mount -d redis://127.0.0.1:6379/1 /data/jfs --no-usage-report &

      - name: Install Elasticsearch
        run: |
          wget -qO - https://artifacts.elastic.co/GPG-KEY-elasticsearch | sudo apt-key add -
          sudo sh -c 'echo "deb https://artifacts.elastic.co/packages/7.x/apt stable main" > /etc/apt/sources.list.d/elastic-7.x.list'
          sudo .github/scripts/apt_install.sh apt-transport-https elasticsearch

      - name: Change Datadir
        run: |
          sudo chmod 777 /data/jfs/
          sudo service elasticsearch stop
          sudo sed  -i "s?^path\.data.*?path\.data: /data/jfs/?" /etc/elasticsearch/elasticsearch.yml
          sudo service elasticsearch restart
          curl -X GET "localhost:9200/"

      - name: Esrally Test
        run: |
          sudo pip3 install esrally
          sudo .github/scripts/apt_install.sh pbzip2
          esrally race --track=geopoint --target-hosts=127.0.0.1:9200 --pipeline=benchmark-only --report-file=/tmp/report.md
          cat /tmp/report.md

      - name: Log
        if: failure()
        run: |
          echo "juicefs log"
          sudo tail -n 1000 /var/log/juicefs.log
          grep "<FATAL>:" /var/log/juicefs.log && exit 1

      - name: Send Slack Notification
        if: failure()
        uses: juicedata/slack-notify-action@main
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID_FOR_PR_CHECK_NOTIFY }}"
          slack_bot_token: "${{ secrets.SLACK_BOT_TOKEN }}"  

      - name: Setup upterm session
        if: failure() && github.event_name == 'workflow_dispatch' && github.event.inputs.debug
        timeout-minutes: 60
        uses: lhotari/action-upterm@v1