name: "mongodb_mmap"

on:
  push:
    branches:
      - 'release-**'
    paths-ignore:
      - 'docs/**'
  pull_request:
    #The branches below must be a subset of the branches above
    branches:
      - 'release-**'
    paths-ignore:
      - 'docs/**'
  schedule:
    - cron:  '30 20 * * *'
  workflow_dispatch:
    inputs:
      debug:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false


jobs:
  mongodb_mmap:
    runs-on: ubuntu-20.04
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 1

      - name: Build
        uses: ./.github/actions/build

      - name: Run Redis
        run: |
          sudo docker run -d --name redis -v redis-data:/data  \
          -p 6379:6379  redis redis-server --appendonly yes

      - name: Juicefs Format
        run: |
          sudo ./juicefs format --trash-days 0 redis://127.0.0.1:6379/1 pics

      - name: Juicefs Mount
        run: |
          sudo ./juicefs mount -d redis://127.0.0.1:6379/1 /jfs/ --enable-xattr --cache-size 3072 --no-usage-report &

      - name: Set up Mongodb
        run: |
          cd /jfs/
          git clone https://github.com/sanwan/actionsoftware.git
          cd actionsoftware
          tar zxvf mongodb-linux-x86_64-3.0.0.tgz
          cd mongodb-linux-x86_64-3.0.0
          mkdir /jfs/mongodb/
          nohup ./bin/mongod --dbpath /jfs/mongodb &

      - name: Insert Data
        run: |
          cd /jfs/
          wget -O ycsb.tar.gz -q https://github.com/brianfrankcooper/YCSB/releases/download/0.17.0/ycsb-0.17.0.tar.gz
          tar -zxvf ycsb.tar.gz
          cd ycsb-0.17.0
          sed -i "s?recordcount=1000?recordcount=1000000?" workloads/workloadf
          sed -i "s?operationcount=1000?operationcount=15000?" workloads/workloadf
          cat workloads/workloadf
          wget https://raw.githubusercontent.com/adonis0147/YCSB/master/bin/ycsb -O bin/ycsb
          time ./bin/ycsb load mongodb -s -P workloads/workloadf  -threads 10 > outputLoad.txt

      - name: Read Modify and Write
        run: |
          cd /jfs/ycsb-0.17.0/
          ps -aux | grep mongo
          echo "run read modify write"
          time ./bin/ycsb run mongodb -s -P workloads/workloadf -threads 10 > outputRun.txt

      - name: Log
        if: always()
        run: |
          echo "juicefs log"
          sudo tail -n 1000 /var/log/juicefs.log
          grep "<FATAL>:" /var/log/juicefs.log && exit 1 || true

      - name: Send Slack Notification
        if: failure()
        uses: juicedata/slack-notify-action@main
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID_FOR_PR_CHECK_NOTIFY }}"
          slack_bot_token: "${{ secrets.SLACK_BOT_TOKEN }}"  

      - name: Setup upterm session
        if: failure() && (github.event.inputs.debug == 'true' || github.run_attempt != 1)
        # if: failure()
        timeout-minutes: 60
        uses: lhotari/action-upterm@v1