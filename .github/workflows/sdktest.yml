name: "sdktest"

on:
  push:
    branches:
      - 'main'
      - 'release-*'
    paths-ignore:
      - 'docs/**'
      - '**.md'
  pull_request:
    #The branches below must be a subset of the branches above
    branches:
      - 'main'
      - 'release-*'
    paths-ignore:
      - 'docs/**'
      - '**.md'
      - '.github/**'
  workflow_dispatch:
    inputs:
      debug:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false

jobs:
  sdktest:
    timeout-minutes: 50
    runs-on: ubuntu-20.04
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 1

      - name: Build
        uses: ./.github/actions/build

      - name: Set up Java
        uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: '8'

      - name: Run Redis
        run: |
          sudo docker run -d --name redis -v redis-data:/data  \
          -p 6379:6379  redis redis-server --appendonly yes

      - name: Juicefs Format
        run: |
          sudo ./juicefs format  localhost dev

      - name: Juicefs Mount
        run: |
          sudo ./juicefs mount -d localhost /jfs
          touch /jfs/inner_sym_target
          echo "hello juicefs" > /jfs/inner_sym_target
          cd /jfs
          ln -s inner_sym_target inner_sym_link 
          mkdir etc
          chmod 777 etc
          echo `hostname` > etc/nodes

      - name: Sdk Test
        run: |
          make -C sdk/java/libjfs
          cd sdk/java
          sudo mvn test -B
          sudo mvn package -B -Dmaven.test.skip=true --quiet -Dmaven.javadoc.skip=true

      - name: Send Slack Notification
        if: failure()
        uses: juicedata/slack-notify-action@main
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID_FOR_PR_CHECK_NOTIFY }}"
          slack_bot_token: "${{ secrets.SLACK_BOT_TOKEN }}"  

      - name: Setup upterm session
        if: failure() && (github.event.inputs.debug == 'true' || github.run_attempt != 1)
        timeout-minutes: 60
        uses: lhotari/action-upterm@v1
