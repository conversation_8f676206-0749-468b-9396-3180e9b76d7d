   upstream backend {
      server 127.0.0.1:9000;
      server 127.0.0.1:9001;
   }

   # This server accepts all traffic to port 80 and passes it to the upstream.
   # Notice that the upstream name and the proxy_pass need to match.

   server {
      listen 8080;
      server_name localhost;
      location / {
          proxy_set_header Host $http_host;
          proxy_pass http://backend;
      }
   }

   client_max_body_size 100M;