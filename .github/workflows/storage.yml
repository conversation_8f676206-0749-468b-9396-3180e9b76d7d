name: "storage"

on:
  push:
    branches:
      - 'release-**'
      - 'main'
    paths:
      - 'pkg/object/*.go'
      - '**/storage.yml'
      - '**/storage.sh'
  pull_request:
    branches:
      - 'release-**'
      - 'main'
    paths:
      - 'pkg/object/*.go'
      - '**/storage.yml'
      - '**/storage.sh'
  schedule:
    - cron:  '30 20 * * *'
  workflow_dispatch:

jobs:
  storage:
    strategy:
      fail-fast: false
      matrix:
        storage: ['gluster', 'minio']
    runs-on: ubuntu-20.04
    steps:
      - name: Remove unused software
        shell: bash
        run: |
            echo "before remove unused software"
            sudo df -h
            sudo rm -rf /usr/share/dotnet
            sudo rm -rf /usr/local/lib/android
            sudo rm -rf /opt/ghc
            echo "after remove unused software"
            sudo df -h

      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 1

      - name: Set environment variables
        run: |
          if [ "${{ matrix.storage }}" = "gluster" ]; then
            echo "set TARGET to juicefs.gluster"
            echo "TARGET=juicefs.gluster" >> $GITHUB_ENV
          else
            echo "set TARGET to juicefs"
            echo "TARGET=juicefs" >> $GITHUB_ENV
          fi

      - name: Build
        uses: ./.github/actions/build
        with: 
          target: "$TARGET"
          # useBeta: true

      - name: Test
        run: |
          sudo STORAGE=${{matrix.storage}} .github/scripts/storage.sh

      - name: Setup upterm session
        if: failure() && (github.event.inputs.debug == 'true' || github.run_attempt != 1)
        # if: failure()
        timeout-minutes: 60
        uses: lhotari/action-upterm@v1

  success-all-test:
    runs-on: ubuntu-latest
    needs: [storage]
    if: always()
    steps:
      - uses: technote-space/workflow-conclusion-action@v3
      - uses: actions/checkout@v3

      - name: Check Failure
        if: env.WORKFLOW_CONCLUSION == 'failure'
        run: exit 1

      - name: Send Slack Notification
        if: failure() && github.event_name != 'workflow_dispatch'
        uses: juicedata/slack-notify-action@main
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID_FOR_PR_CHECK_NOTIFY }}"
          slack_bot_token: "${{ secrets.SLACK_BOT_TOKEN }}"

      - name: Success
        if: success()
        run: echo "All Done"