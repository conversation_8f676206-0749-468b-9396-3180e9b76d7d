name: "load-test"
on:
  push:
    branches:
      - 'main'
      - 'release-**'
    paths-ignore:
      - 'docs/**'
      - '**.md'
  pull_request:
    branches:
      - 'main'
      - 'release-**'
    paths-ignore:
      - 'docs/**'
      - '**.md'
  schedule:
    - cron:  '0 19 * * *'
  workflow_dispatch:
    inputs:
      debug:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false

jobs:
  build-matrix:
    runs-on: ubuntu-20.04
    steps:
      - id: set-matrix
        run: |
          echo "github.event_name is ${{github.event_name}}"
          echo "GITHUB_REF_NAME is ${GITHUB_REF_NAME}"
          if [ "${{github.event_name}}" == "schedule"  ] || [ "${{github.event_name}}" == "workflow_dispatch"  ]; then
            echo 'meta_matrix=["sqlite3", "redis", "mysql", "tikv", "tidb", "postgres", "mariadb", "fdb"]' >> $GITHUB_OUTPUT
          else
            echo 'meta_matrix=["redis", "mysql", "tikv"]' >> $GITHUB_OUTPUT
            # echo 'meta_matrix=["redis"]' >> $GITHUB_OUTPUT
          fi
    outputs:
      meta_matrix: ${{ steps.set-matrix.outputs.meta_matrix }}

  load:
    if: github.repository == 'juicedata/juicefs'
    needs: [build-matrix]
    strategy:
      fail-fast: false
      matrix:
        meta: ${{ fromJson(needs.build-matrix.outputs.meta_matrix) }}

    runs-on: ubuntu-20.04

    steps:
      - name: Remove unused software
        if: false
        shell: bash
        run: |
            echo "before remove unused software"
            sudo df -h
            sudo rm -rf /usr/share/dotnet
            sudo rm -rf /usr/local/lib/android
            sudo rm -rf /opt/ghc
            echo "after remove unused software"
            sudo df -h

      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 1

      - name: Set Variable
        id: vars
        run: |
          if [ "${{matrix.meta}}" == "fdb" ]; then
            echo "target=juicefs.fdb" >> $GITHUB_OUTPUT
          else
            echo "target=juicefs" >> $GITHUB_OUTPUT
          fi

      - name: Build
        uses: ./.github/actions/build
        with: 
          target: ${{steps.vars.outputs.target}}

      - name: Load and dump with small directory
        timeout-minutes: 30
        run: |
          sudo MYSQL_PASSWORD=${{secrets.MYSQL_PASSWORD_FOR_JUICEDATA}} META=${{matrix.meta}} START_META=true .github/scripts/command/load_dump_bench.sh test_load_dump_with_small_dir

      - name: Load and dump with big directory
        timeout-minutes: 30
        run: |
          sudo MYSQL_PASSWORD=${{secrets.MYSQL_PASSWORD_FOR_JUICEDATA}} META=${{matrix.meta}} START_META=false .github/scripts/command/load_dump_bench.sh test_load_dump_with_big_dir
      
      - name: Load and dump subdir with big directory
        if: false
        timeout-minutes: 30
        run: |
          sudo MYSQL_PASSWORD=${{secrets.MYSQL_PASSWORD_FOR_JUICEDATA}} META=${{matrix.meta}} START_META=false .github/scripts/command/load_dump_bench.sh test_load_dump_with_big_dir_subdir
          
      - name: List big directory
        timeout-minutes: 30
        run: | 
          sudo MYSQL_PASSWORD=${{secrets.MYSQL_PASSWORD_FOR_JUICEDATA}} META=${{matrix.meta}} START_META=false .github/scripts/command/load_dump_bench.sh test_list_with_big_dir

      - name: log
        if: always()
        shell: bash
        run: | 
          if [ -f ~/.juicefs/juicefs.log ]; then
            tail -300 ~/.juicefs/juicefs.log
            grep "<FATAL>:" ~/.juicefs/juicefs.log && exit 1 || true
          fi
          
      - name: Setup upterm session
        if: failure() && (github.event.inputs.debug == 'true' || github.run_attempt != 1)
        # if: failure()
        timeout-minutes: 60
        uses: lhotari/action-upterm@v1

  success-all-test:
    runs-on: ubuntu-latest
    needs: [load]
    if: always()
    steps:
      - uses: technote-space/workflow-conclusion-action@v3
      - uses: actions/checkout@v3

      - name: Check Failure
        if: env.WORKFLOW_CONCLUSION == 'failure'
        run: exit 1

      - name: Send Slack Notification
        if: failure() && github.event_name != 'workflow_dispatch' 
        uses: juicedata/slack-notify-action@main
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID_FOR_PR_CHECK_NOTIFY }}"
          slack_bot_token: "${{ secrets.SLACK_BOT_TOKEN }}"

      - name: Success
        if: success()
        run: echo "All Done"
        