name: "mysqltest"

on:
#  push:
#    branches:
#      - 'release-**'
#    paths-ignore:
#      - 'docs/**'
#  pull_request:
#    #The branches below must be a subset of the branches above
#    branches:
#      - 'release-**'
#    paths-ignore:
#      - 'docs/**'
  schedule:
    - cron:  '30 20 * * *'
  workflow_dispatch:

jobs:
  mysqltest:
    strategy:
      fail-fast: false
      matrix:
        size: [ 'big', 'small' ]
    runs-on: ubuntu-20.04
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 1

      - name: Build
        uses: ./.github/actions/build

      - name: Run Redis
        run: |
          sudo docker run -d --name redis -v redis-data:/data  \
          -p 6379:6379  redis redis-server --appendonly yes

      - name: Juicefs Format
        run: |
          sudo ./juicefs format --trash-days 0 redis://127.0.0.1:6379/1 pics

      - name: Juicefs Mount
        run: |
          sudo ./juicefs mount -d redis://127.0.0.1:6379/1 /tmp/jfs --no-usage-report &


      - name: Change Data Dir
        run: |
          sudo service mysql stop
          sudo sed  -i "s?.*datadir.*?datadir=/tmp/jfs/mysql?" /etc/mysql/mysql.conf.d/mysqld.cnf
          sudo cat /etc/mysql/mysql.conf.d/mysqld.cnf
          sudo mkdir /tmp/jfs/mysql
          sudo chmod 777 /var/lib/mysql/
          sudo cp -a -r /var/lib/mysql/* /tmp/jfs/mysql/
          sudo chmod 777 /tmp/jfs/mysql/
          sudo chown -R mysql:mysql /tmp/jfs/mysql/
          sudo chmod 777 /etc/apparmor.d/tunables/alias
          sudo echo "alias /var/lib/mysql/ -> /tmp/jfs/mysql/," >>/etc/apparmor.d/tunables/alias
          sudo service apparmor restart
          sudo service mysql start
          sudo service mysql status
          sudo mysql -uroot -proot  -e "select @@datadir;"

      - name: Change Parameters
        run: |
          mysql -uroot -proot  -e "show variables like 'log_error';"
          mysql -uroot -proot -e "set global log_error_verbosity=3;"
          mysql -uroot -proot  -e "show variables like 'log_error_verbosity';"
          mysql -uroot -proot -e "set global max_prepared_stmt_count=500000;"

      - name: Install Sysbench
        run: |
          curl -s https://packagecloud.io/install/repositories/akopytov/sysbench/script.deb.sh | sudo bash
          sudo .github/scripts/apt_install.sh sysbench
          sudo mysql -uroot -proot -e "create database test;"
          echo "mysql-host=127.0.0.1" >>/tmp/config.cfg
          echo "mysql-port=3306" >>/tmp/config.cfg
          echo "mysql-user=root" >>/tmp/config.cfg
          echo "mysql-password=root" >>/tmp/config.cfg
          echo "mysql-db=test" >>/tmp/config.cfg
          echo "report-interval=10" >>/tmp/config.cfg
          echo "db-driver=mysql" >>/tmp/config.cfg

      - name: Big Size Table Test
        run: |
          if [ "${{matrix.size}}" == "big"]; then
            echo "threads=8" >>/tmp/config.cfg
            TABLE_SIZE=20000
            tables=4
          else
            echo "threads=16" >>/tmp/config.cfg
            TABLE_SIZE=10000
            tables=8
          fi
          for item in oltp_point_select oltp_read_only oltp_write_only oltp_read_write oltp_point_select; do
              sudo sysbench --config-file=/tmp/config.cfg $item --tables=$tables --time=300 --table-size=$TABLE_SIZE prepare
              sudo sysbench --config-file=/tmp/config.cfg $item --tables=$tables --time=300 --table-size=$TABLE_SIZE run
              sudo sysbench --config-file=/tmp/config.cfg $item --tables=$tables --time=300 --table-size=$TABLE_SIZE cleanup
          done

      - name: Log
        if: always()
        run: |
          echo "juicefs log"
          sudo tail -n 1000 /var/log/juicefs.log
          grep "<FATAL>:" /var/log/juicefs.log && exit 1 || true

      - name: Send Slack Notification
        if: failure()
        uses: juicedata/slack-notify-action@main
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID_FOR_PR_CHECK_NOTIFY }}"
          slack_bot_token: "${{ secrets.SLACK_BOT_TOKEN }}"  
