name: "filebench"

on:
  push:
    branches:
      - 'release-**'
    paths-ignore:
      - 'docs/**'
      - '**.md'
  schedule:
    - cron:  '30 20 * * *'
  workflow_dispatch:


jobs:
  filebench:
    timeout-minutes: 60
    strategy:
      fail-fast: false
      matrix:
        workload: [ 'varmail', 'webserver', 'videoserver']
    runs-on: ubuntu-20.04
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 1
      
      - name: Build
        uses: ./.github/actions/build

      - name: Run Redis
        run: |
          sudo docker run -d --name redis -v redis-data:/data  \
          -p 6379:6379  redis redis-server --appendonly yes

      - name: Setup minio
        run: |
          docker run -d -p 9000:9000 --name minio \
                    -e "MINIO_ACCESS_KEY=minioadmin" \
                    -e "MINIO_SECRET_KEY=minioadmin" \
                    -v /tmp/data:/data \
                    -v /tmp/config:/root/.minio \
                    minio/minio server /data
          sleep 5

      - name: Juicefs Format
        run: |
          sudo ./juicefs format --trash-days 0  --storage minio --bucket http://127.0.0.1:9000/mypics \
            --access-key minioadmin \
            --secret-key minioadmin \
            redis://127.0.0.1:6379/1 pics

      - name: Juicefs Mount
        run: |
          sudo ./juicefs mount -d redis://127.0.0.1:6379/1 /data/jfs --no-usage-report &

      - name: Install Dependency
        run: |
          wget -O filebench.tar.gz https://github.com/filebench/filebench/releases/download/1.5-alpha3/filebench-1.5-alpha3.tar.gz
          tar -zxvf filebench.tar.gz
          cd filebench-1.5-alpha3
          ./configure
          make
          sudo make install
          filebench -h
          echo 0 >randomize_va_space
          sudo cp randomize_va_space /proc/sys/kernel/
          echo "randomize_va_space"
          cat /proc/sys/kernel/randomize_va_space

      - name: Run Workload
        run: |
          cd /usr/local/share/filebench/workloads/
          sudo chmod 777 *
          if [ ${{matrix.workload}} == "varmail" ];then
            echo "run varmail workload"
            sudo sed -i "s?/tmp?/data/jfs?" varmail.f
            sudo sed -i "s?run 60?run 1800?" varmail.f
            cat varmail.f
            sudo filebench -f varmail.f
          elif [ ${{matrix.workload}} == "webserver" ];then
            echo "run webserver workload"
            sudo sed -i "s?/tmp?/data/jfs?" webserver.f
            sudo sed -i "s?run 60?run 1800?" webserver.f
            cat webserver.f
            sudo filebench -f webserver.f
          elif [ ${{matrix.workload}} == "videoserver" ];then
            echo "run videoserver worload"
            sudo sed -i "s?/tmp?/data/jfs?" videoserver.f
            sudo sed -i "s?$filesize=10g?$filesize=100m?" videoserver.f
            sudo sed -i "s?$numpassivevids=194?$numpassivevids=100?" videoserver.f
            sudo echo "run 1800" >>videoserver.f
            cat videoserver.f
            sudo filebench -f videoserver.f
          fi

      - name: Log
        if: always()
        run: |
          df -lh
          echo "juicefs log"
          sudo tail -n 1000 /var/log/juicefs.log
          grep "<FATAL>:" /var/log/juicefs.log && exit 1 || true
