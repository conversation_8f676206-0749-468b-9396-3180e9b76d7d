name: "integrationtests"

on:
  push:
    branches:
      - 'main'
      - 'release-*'
    paths-ignore:
      - 'docs/**'
      - '**.md'
  pull_request:
    #The branches below must be a subset of the branches above
    branches:
      - 'main'
      - 'release-*'
    paths-ignore:
      - 'docs/**'
      - '**.md'
      - '.github/**'
  workflow_dispatch:
    inputs:
      debug:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false

jobs:
  integrationtests:
    timeout-minutes: 60
    runs-on: ubuntu-20.04
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 1

      - name: Build
        uses: ./.github/actions/build

      - name: Copy
        run: |
          cp juicefs /tmp/mount.juicefs

      - name: Run Redis
        run: |
          sudo docker run -d --name redis -v redis-data:/data  \
          -p 6379:6379  redis redis-server --appendonly yes

      - name: Juicefs Format
        run: |
          sudo ./juicefs format  redis://127.0.0.1:6379/1 pics

      - name: Juicefs Mount
        run: |
          sudo ./juicefs mount -d redis://127.0.0.1:6379/1 /jfs/ --enable-xattr --no-usage-report &

      - name: Fslock Test
        run: |
          cd /jfs/
          git clone https://github.com/danjacques/gofslock.git
          cd gofslock && git checkout cc7f001fe0e7df1710adc8f0cd9e9d6d21fdb3a9
          go test -v ./fslock/...
          stat /jfs/


      - name: Pyxattr Test
        run: |
          git clone https://github.com/iustin/pyxattr.git
          cd pyxattr
          pip3 install pytest
          pip3 install pyxattr
          stat /jfs/
          TEST_DIR=/jfs/ python3 -m pytest tests


      - name: Fstests
        run: |
          sudo .github/scripts/apt_install.sh  libacl1-dev attr
          sudo DURATION=60 make -C fstests fsx
          sudo make -C fstests xattrs
          sudo make -C fstests flock


      - name: Fsracer
        #https://github.com/gfx/example-github-actions-with-tty
        shell: 'script -q -e -c "bash {0}"'
        run: |
          sudo DURATION=60 make -C fstests fsracer

      - name: MountJuiceFSTest
        run: |
          sudo /tmp/mount.juicefs redis://127.0.0.1:6379/1 /tmp/mount-jfs
          [ `stat --format "%i" /tmp/mount-jfs` -eq 1 ]


      - name: log
        if: always()
        run: |
          tail -300 /var/log/juicefs.log
          grep "<FATAL>:" /var/log/juicefs.log && exit 1 || true

      - name: Send Slack Notification
        if: failure()
        uses: juicedata/slack-notify-action@main
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID_FOR_PR_CHECK_NOTIFY }}"
          slack_bot_token: "${{ secrets.SLACK_BOT_TOKEN }}"


      - name: Setup upterm session
        if: failure() && (github.event.inputs.debug == 'true' || github.run_attempt != 1)
        timeout-minutes: 60
        uses: lhotari/action-upterm@v1
