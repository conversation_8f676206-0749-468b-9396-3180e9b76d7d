name: "rediscompile"

on:
  push:
    branches:
      - 'release-**'
    paths-ignore:
      - 'docs/**'
  pull_request:
    #The branches below must be a subset of the branches above
    branches:
      - 'release-**'
    paths-ignore:
      - 'docs/**'
  schedule:
    - cron:  '30 20 * * *'
  workflow_dispatch:

jobs:
  rediscompile:
    runs-on: ubuntu-20.04
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 1

      - name: Build
        uses: ./.github/actions/build

      - name: Setup minio
        run: |
          docker run -d -p 9000:9000 --name minio \
                    -e "MINIO_ACCESS_KEY=minioadmin" \
                    -e "MINIO_SECRET_KEY=minioadmin" \
                    -v /tmp/data:/data \
                    -v /tmp/config:/root/.minio \
                    minio/minio server /data
          sleep 5

      - name: Juicefs Format
        run: |
          sudo ./juicefs format --storage minio --bucket http://127.0.0.1:9000/mypics \
                --access-key minioadmin \
                --secret-key minioadmin \
                sqlite3:///tmp/myjfs.db pics

      - name: Juicefs Mount
        run: |
          sudo ./juicefs mount -d sqlite3:///tmp/myjfs.db /tmp/jfs --no-usage-report


      - name: Compile Redis
        run: |
          cd /tmp/jfs
          wget -O redis.tar.gz https://github.com/redis/redis/archive/refs/tags/6.0.16.tar.gz
          mkdir redis
          tar -xvf redis.tar.gz -C redis --strip-components 1
          cd redis
          make
          make test
          stat /tmp/jfs
          df -lh


      - name: Log
        if: always()
        run: |
          echo "juicefs log"
          sudo tail -n 1000 /var/log/juicefs.log
          grep "<FATAL>:" /var/log/juicefs.log && exit 1 || true

      - name: Send Slack Notification
        if: failure()
        uses: juicedata/slack-notify-action@main
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID_FOR_PR_CHECK_NOTIFY }}"
          slack_bot_token: "${{ secrets.SLACK_BOT_TOKEN }}"  
