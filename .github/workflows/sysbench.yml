name: "sysbench"

on:
  schedule:
    - cron:  '30 20 * * *'
  workflow_dispatch:

jobs:
  sysbench:
    strategy:
      fail-fast: false
      matrix:
        meta: [ 'sqlite3', 'redis', 'mysql', 'tikv', 'tidb', 'postgres', 'mariadb', 'badger', 'fdb']
        # meta: ['redis']
        compress: [ 'lz4']
        include: 
          - meta: 'redis'
            compress: 'zstd'
          - meta: 'redis'
            compress: 'none'

    runs-on: ubuntu-20.04
  
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 1

      - name: Set Variable
        id: vars
        run: |
          if [ "${{matrix.meta}}" == "fdb" ]; then
            echo "target=juicefs.fdb" >> $GITHUB_OUTPUT
          else
            echo "target=juicefs" >> $GITHUB_OUTPUT
          fi

      - name: Build
        uses: ./.github/actions/build
        with: 
          target: ${{steps.vars.outputs.target}}

      - name: Prepare meta db
        run: | 
          chmod +x .github/scripts/start_meta_engine.sh
          source .github/scripts/start_meta_engine.sh
          start_meta_engine ${{matrix.meta}}
          meta_url=$(get_meta_url ${{matrix.meta}})
          create_database $meta_url

      - name: Setup minio
        run: |
          docker run -d -p 19000:9000 --name minio \
                    -e "MINIO_ACCESS_KEY=minioadmin" \
                    -e "MINIO_SECRET_KEY=minioadmin" \
                    -v /tmp/data:/data \
                    -v /tmp/config:/root/.minio \
                    minio/minio server /data
          sleep 5

      - name: Juicefs Format
        run: |
          source .github/scripts/start_meta_engine.sh
          meta_url=$(get_meta_url ${{matrix.meta}})
          sudo ./juicefs format --trash-days 0 --compress ${{matrix.compress}} --storage minio --bucket http://127.0.0.1:19000/mypics \
            --access-key minioadmin \
            --secret-key minioadmin \
            $meta_url pics

      - name: Juicefs Mount
        run: |
          source .github/scripts/start_meta_engine.sh
          meta_url=$(get_meta_url ${{matrix.meta}})
          sudo ./juicefs mount -d $meta_url /jfs --no-usage-report &

      - name: Install Sysbench
        run: |
          curl -s https://packagecloud.io/install/repositories/akopytov/sysbench/script.deb.sh | sudo bash
          sudo .github/scripts/apt_install.sh  sysbench
          sudo pip install mysqlclient
          date +%Y_%m_%d_%H_%M_%S > /tmp/datetime

      - name: seq read 1M
        uses: ./.github/actions/sysbench
        with:
          mysql_password: ${{secrets.MYSQL_PASSWORD_FOR_JUICEDATA}}
          file_num: 1000
          file_total_size: '1G'
          file_test_mode: 'seqrd'
          meta: ${{matrix.meta}}
          storage: 'minio'
          name: 'sysbench_seq_read_1m'
          compress: ${{matrix.compress}}

      - name: seq write 1M
        uses: ./.github/actions/sysbench
        with:
          mysql_password: ${{secrets.MYSQL_PASSWORD_FOR_JUICEDATA}}
          file_num: 1000
          file_total_size: '1G'
          file_test_mode: 'seqwr'
          meta: ${{matrix.meta}}
          storage: 'minio'
          name: 'sysbench_seq_write_1m'
          compress: ${{matrix.compress}}

      - name: seq read 100K
        uses: ./.github/actions/sysbench
        with:
          mysql_password: ${{secrets.MYSQL_PASSWORD_FOR_JUICEDATA}}
          file_num: 10000
          file_total_size: '1G'
          file_test_mode: 'seqrd'
          meta: ${{matrix.meta}}
          storage: 'minio'
          name: 'sysbench_seq_read_100k'
          compress: ${{matrix.compress}}

      - name: seq write 100K
        uses: ./.github/actions/sysbench
        with:
          mysql_password: ${{secrets.MYSQL_PASSWORD_FOR_JUICEDATA}}
          file_num: 10000
          file_total_size: '1G'
          file_test_mode: 'seqwr'
          meta: ${{matrix.meta}}
          storage: 'minio'
          name: 'sysbench_seq_write_100k'
          compress: ${{matrix.compress}}

      - name: random readwrite 1M
        uses: ./.github/actions/sysbench
        with:
          mysql_password: ${{secrets.MYSQL_PASSWORD_FOR_JUICEDATA}}
          file_num: 1000
          file_total_size: '1G'
          file_test_mode: 'rndrw'
          meta: ${{matrix.meta}}
          storage: 'minio'
          name: 'sysbench_rand_rw_1m'
          compress: ${{matrix.compress}}

      - name: random readwrite 100K
        uses: ./.github/actions/sysbench
        with:
          mysql_password: ${{secrets.MYSQL_PASSWORD_FOR_JUICEDATA}}
          file_num: 10000
          file_total_size: '1G'
          file_test_mode: 'rndrw'
          meta: ${{matrix.meta}}
          storage: 'minio'
          name: 'sysbench_rand_rw_100k'
          compress: ${{matrix.compress}}

      - name: Send Slack Notification
        if: failure()
        uses: juicedata/slack-notify-action@main
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID_FOR_PR_CHECK_NOTIFY }}"
          slack_bot_token: "${{ secrets.SLACK_BOT_TOKEN }}"

