name: Check document

on:
  push:
    branches: [main]
    paths:
      - 'README*.md'
      - 'docs/**'
      - 'package.json'
      - '.autocorrectrc'
      - '.markdownlint-cli2.jsonc'
      - '.github/workflows/check-doc.yaml'
  pull_request:
    branches: [main]
    paths:
      - 'README*.md'
      - 'docs/**'
      - 'package.json'
      - '.autocorrectrc'
      - '.markdownlint-cli2.jsonc'
      - '.github/workflows/check-doc.yaml'

jobs:
  check-doc:
    name: Check document
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16.x'
          cache: 'npm'
      - name: Install dependencies
        run: |
          npm ci
      - name: Lint Markdown files (markdownlint)
        run: |
          npm run markdown-lint
      - name: Lint Markdown files (autocorrect)
        uses: huacnlee/autocorrect-action@main
        with:
          args: --lint ./docs/
      - name: Check broken link (including broken anchor)
        run: |
          npm run check-broken-link
