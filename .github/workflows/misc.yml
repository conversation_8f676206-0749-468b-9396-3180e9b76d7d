name: "misc-test"

on:
  push:
    branches: 
      - release-**
    paths-ignore:
      - 'docs/**'
      - '**.md'
  pull_request:
    #The branches below must be a subset of the branches above
    branches: 
      - release-**
      # - main
    paths-ignore:
      - 'docs/**'
      - '**.md'
  workflow_dispatch:
  schedule:
    - cron:  '0 0 * * *'
jobs:
  misc-test:
    runs-on: ubuntu-20.04
    services:
      redis:
        # Docker Hub image
        image: redis
        # Set health checks to wait until redis has started
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          # Maps port 6379 on service container to the host
          - 6379:6379
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 1

      - name: Setup minio
        run: |
          docker run -d -p 9000:9000 --name minio \
                    -e "MINIO_ACCESS_KEY=minioadmin" \
                    -e "MINIO_SECRET_KEY=minioadmin" \
                    -v /tmp/data:/data \
                    -v /tmp/config:/root/.minio \
                    minio/minio server /data     

      - name: Build 
        uses: ./.github/actions/build

      - name: Install tool
        run: | 
          wget -q https://dl.minio.io/client/mc/release/linux-amd64/mc
          chmod +x mc 
          sudo .github/scripts/apt_install.sh redis-tools fio

      - name: Test mount process exit on reformat
        run: |
          for i in {1..3}; do 
            echo "round $i"
            meta=redis://localhost/1
            ./juicefs format $meta volume-$i
            ./juicefs mount -d $meta /tmp/myjfs$i_$j --no-usage-report
            cd /tmp/myjfs$i_$j
            bash -c 'for k in {1..300}; do echo abc>$k; sleep 0.2; done' || true & 
            cd -
            sleep 3
            uuid=$(./juicefs status $meta | grep UUID | cut -d '"' -f 4) 
            ./juicefs destroy --force $meta $uuid
            ./juicefs format $meta new-volume-$i 
            sleep 15   
            ps -ef | grep juicefs
            pidof juicefs && exit 1
            uuid=$(./juicefs status $meta | grep UUID | cut -d '"' -f 4) 
            ./juicefs destroy --force $meta $uuid
          done

      - name: Test config secret key
        run: |
          # # Consider command as failed when any component of the pipe fails:
          # https://stackoverflow.com/questions/1221833/pipe-output-and-capture-exit-status-in-bash
          set -o pipefail
          meta_url=redis://127.0.0.1/2
          mount_point=/tmp/sync-test/
          ./mc config host add minio http://127.0.0.1:9000 minioadmin minioadmin
          ./mc admin user add minio juicedata juicedata
          ./mc admin policy attach minio consoleAdmin --user juicedata
          ./juicefs format --storage minio --bucket http://localhost:9000/jfs-test --access-key juicedata --secret-key juicedata $meta_url myjfs
          ./juicefs mount $meta_url $mount_point -d --io-retries 1 --no-usage-report --heartbeat 5

          ./mc admin user remove minio juicedata
          ./mc admin user add minio juicedata1 juicedata1
          ./mc admin policy attach minio consoleAdmin --user juicedata1
          ./juicefs config $meta_url --access-key juicedata1 --secret-key juicedata1
          sleep 6
          echo abc | tee $mount_point/abc.txt && echo "write success"
          cat $mount_point/abc.txt | grep abc && echo "read success"
          
      - name: Test delay delete slice after compaction
        run: |
          meta=redis://localhost/3
          mp=myjfs/
          name=myjfs2
          juicefs=./juicefs
          $juicefs format $meta $name --trash-days 1
          $juicefs mount -d $meta $mp --no-usage-report
          fio --name=abc --rw=randwrite --refill_buffers --size=500M --bs=256k --directory=$mp
          redis-cli save
          # don't skip files when gc compact
          export JFS_SKIPPED_TIME=1
          $juicefs gc --compact --delete $meta
          container_id=$(docker ps -a | grep redis | awk '{print $1}')
          sudo killall -9 redis-server
          sudo docker restart $container_id
          sleep 3
          $juicefs fsck $meta

      - name: log
        if: always()
        run: | 
          tail -300 ~/.juicefs/juicefs.log
          grep -v "UUID changed"  ~/.juicefs/juicefs.log | grep "<FATAL>:"  && exit 1 || true

      - name: Send Slack Notification
        if: failure()
        uses: juicedata/slack-notify-action@main
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID_FOR_PR_CHECK_NOTIFY }}"
          slack_bot_token: "${{ secrets.SLACK_BOT_TOKEN }}"
