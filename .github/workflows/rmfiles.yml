name: "rmr-test"

on:
  push:
    branches:
      - 'release-**'
    paths-ignore:
      - 'docs/**'
      - '**.md'
  schedule:
    - cron:  '0 20 * * *'
  workflow_dispatch:

jobs:
  rmr-test:
    strategy:
      fail-fast: false
      matrix:
        meta: [ 'sqlite3', 'redis', 'mysql',  'postgres', 'tikv', 'fdb', 'badger', 'etcd']

    runs-on: ubuntu-20.04

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 1
      
      - name: Set Variable
        id: vars
        run: |
          if [ "${{matrix.meta}}" == "fdb" ]; then
            echo "target=juicefs.fdb" >> $GITHUB_OUTPUT
          else
            echo "target=juicefs" >> $GITHUB_OUTPUT
          fi

      - name: Build
        uses: ./.github/actions/build
        with: 
          target: ${{steps.vars.outputs.target}}

      - name: Prepare meta db
        run: | 
          sudo chmod +x .github/scripts/start_meta_engine.sh
          source .github/scripts/start_meta_engine.sh
          start_meta_engine ${{matrix.meta}}

      - name: Rmr
        shell: bash
        run: |
          source .github/scripts/start_meta_engine.sh
          meta_url=$(get_meta_url ${{matrix.meta}})
          create_database $meta_url
          mp=/tmp/jfs
          # wget -q https://s.juicefs.com/static/bench/500K_empty_files.dump.gz
          # gzip -dk  500K_empty_files.dump.gz
          # ./juicefs load $meta_url 500K_empty_files.dump
          ./juicefs format $meta_url jfs
          ./juicefs mdtest $meta_url test --dirs 10 --depth 3 --files 10 --threads 10 --no-usage-report
          ./juicefs mount -d $meta_url $mp --no-usage-report
          sleep 3
          ls -l $mp/test
          ./juicefs rmr $mp/test/
          sleep 3 
          ls -l $mp/test && exit 1 || true
        
      - name: Clear
        run: | 
          source .github/scripts/start_meta_engine.sh
          meta_url=$(get_meta_url ${{matrix.meta}})
          mp=/tmp/jfs
          volume=jfs
          test -d $mp && ./juicefs umount -f $mp
          ./juicefs status $meta_url && UUID=$(./juicefs status $meta_url | grep UUID | cut -d '"' -f 4) || echo "meta not exist"
          if [ -n "$UUID" ];then
            ./juicefs destroy --yes $meta_url $UUID
          fi
          test -d /var/jfs/$volume && rm -rf /var/jfs/$volume || true
        shell: bash

      - name: Rmr Parallel
        shell: bash
        run: |
          sudo chmod 777 /var
          source .github/scripts/start_meta_engine.sh
          meta_url=$(get_meta_url ${{matrix.meta}})
          mp=/tmp/jfs
          # wget -q https://s.juicefs.com/static/bench/500K_empty_files.dump.gz
          # gzip -dk  500K_empty_files.dump.gz
          # ./juicefs load $meta_url 500K_empty_files.dump
          ./juicefs format $meta_url jfs
          ./juicefs mdtest $meta_url test --dirs 10 --depth 3 --files 15 --threads 10 --no-usage-report
          ./juicefs mount -d $meta_url $mp --no-usage-report
          sleep 3
          declare -a pidlist
          ./juicefs rmr $mp/test/ || true &
          pidlist+=($!)
          ./juicefs rmr $mp/test/ || true &
          pidlist+=($!)
          ./juicefs rmr $mp/test/ || true &
          pidlist+=($!)
          wait "${pidlist[@]}"
          ls -l $mp/test && exit 1 || true

      - name: log
        if: always()
        shell: bash
        run: | 
          tail -300 ~/.juicefs/juicefs.log
          grep "<FATAL>:" ~/.juicefs/juicefs.log && exit 1 || true
          
      - name: Setup upterm session
        if: failure() && (github.event.inputs.debug == 'true' || github.run_attempt != 1)
        timeout-minutes: 60
        uses: lhotari/action-upterm@v1

  success-all-test:
    runs-on: ubuntu-latest
    needs: [rmr-test]
    if: always()
    steps:
      - uses: technote-space/workflow-conclusion-action@v3
      - uses: actions/checkout@v3

      - name: Check Failure
        if: env.WORKFLOW_CONCLUSION == 'failure'
        run: exit 1

      - name: Send Slack Notification
        if: failure() && github.event_name != 'workflow_dispatch'
        uses: juicedata/slack-notify-action@main
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID_FOR_PR_CHECK_NOTIFY }}"
          slack_bot_token: "${{ secrets.SLACK_BOT_TOKEN }}"

      - name: Success
        if: success()
        run: echo "All Done"