name: "version-compatible-test-hypo"

on:
  push:
    branches: 
      - main
    paths:
      - '**/testVersionCompatible.py'
      - '**/version_compatible_hypo.yml'
  pull_request:
    branches: 
      - main
    paths:
      - '**/testVersionCompatible.py'
      - '**/version_compatible_hypo.yml'
  schedule:
    - cron:  '0 19 * * *'
  workflow_dispatch:
    inputs:
      debug:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false

jobs:
  vc-hypo:
    continue-on-error: false
    strategy:
      fail-fast: false
      matrix:
        old_juicefs_version: ['main', 'release-1.0']
        meta: ['redis', 'mysql', 'tikv']
        storage: ['minio']
        include:
          - old_juicefs_version: 'main'
            meta: 'fdb'
            storage: 'minio'
          - old_juicefs_version: 'release-1.0'
            meta: 'postgres'
            storage: 'minio'

    runs-on: ubuntu-20.04

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 1

      - name: Set Variable
        id: vars
        run: |
          if [ "${{matrix.meta}}" == "fdb" ]; then
            echo "target=juicefs.fdb" >> $GITHUB_OUTPUT
          else
            echo "target=juicefs" >> $GITHUB_OUTPUT
          fi

      - name: Build new version
        uses: ./.github/actions/build
        with: 
          target: ${{steps.vars.outputs.target}}

      - name: Checkout old version
        uses: actions/checkout@v3
        with:
          ref: ${{matrix.old_juicefs_version}}
          path: ${{matrix.old_juicefs_version}}

      - name: Make old build
        run: | 
          cd ${{matrix.old_juicefs_version}}
          if [ "${{matrix.meta}}" == "fdb" ]; then
            echo "build juicefs.fdb"
            wget -q https://github.com/apple/foundationdb/releases/download/6.3.23/foundationdb-clients_6.3.23-1_amd64.deb
            sudo dpkg -i foundationdb-clients_6.3.23-1_amd64.deb
            make juicefs.fdb
            mv juicefs.fdb juicefs
          else
            echo "build juicefs"
            make juicefs 
          fi
          cd -

      - name: Prepare meta database
        run: | 
          chmod +x .github/scripts/start_meta_engine.sh
          source .github/scripts/start_meta_engine.sh
          start_meta_engine ${{matrix.meta}} ${{matrix.storage}}
          # meta_url=$(get_meta_url ${{matrix.meta}})
          # create_database $meta_url
          
      - name: Install tools
        run: | 
          wget -q https://dl.minio.io/client/mc/release/linux-amd64/mc
          chmod +x mc
          sudo mv mc /usr/local/bin
          sudo .github/scripts/apt_install.sh redis-tools
          sudo pip install hypothesis
          sudo pip install minio
          sudo pip install xattr
          
      - name: Test
        timeout-minutes: 90
        run: |          
          export META=${{matrix.meta}}
          export STORAGE=${{matrix.storage}}
          new_version=`./juicefs --version | awk -F" " '{print $3}' | awk -F+ '{print $1}'`
          echo new_version is $new_version
          mv juicefs juicefs-$new_version
          export NEW_JFS_BIN="juicefs-$new_version"
          old_version=`${{matrix.old_juicefs_version}}/juicefs --version | awk -F" " '{print $3}' | awk -F+ '{print $1}'`
          echo old_version is $old_version
          mv ${{matrix.old_juicefs_version}}/juicefs juicefs-$old_version
          export OLD_JFS_BIN="juicefs-$old_version"
          timeout 3600 python3 .github/scripts/testVersionCompatible.py || code=$?; if [[ $code -ne 124 && $code -ne 0 ]]; then echo test failed; exit $code; fi

      - name: Upload command log
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: ${{matrix.meta}}-${{matrix.old_juicefs_version}}.command.log
          path: ~/command.log

      - name: Display log
        if: always()
        shell: bash
        run: | 
          if [ -f "/home/<USER>/.juicefs/juicefs.log" ]; then
            cat /home/<USER>/.juicefs/juicefs.log
            grep "<FATAL>:" /home/<USER>/.juicefs/juicefs.log && exit 1 || true
          fi

      - name: Display command
        if: always()
        shell: bash
        run: | 
          if [ -f "$HOME/command.log" ]; then
            tail -100 ~/command.log
          fi

      - name: Setup upterm session
        if: failure() && (github.event.inputs.debug == 'true' || github.run_attempt != 1)
        timeout-minutes: 60
        uses: lhotari/action-upterm@v1


  success-all-test:
    runs-on: ubuntu-latest
    needs: [vc-hypo]
    if: always()
    steps:
      - uses: technote-space/workflow-conclusion-action@v3
      - uses: actions/checkout@v3

      - name: Check Failure
        if: env.WORKFLOW_CONCLUSION == 'failure'
        run: exit 1

      - name: Send Slack Notification
        if: failure() && github.event_name != 'workflow_dispatch'
        uses: juicedata/slack-notify-action@main
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID_FOR_PR_CHECK_NOTIFY }}"
          slack_bot_token: "${{ secrets.SLACK_BOT_TOKEN }}"

      - name: Success
        if: success()
        run: echo "All Done"