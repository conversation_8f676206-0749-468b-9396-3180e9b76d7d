version: '2'
services:
  worker1:
    image: juicedata/ssh
    container_name: worker1
    restart: unless-stopped
    networks:
      static-network:
        ipv4_address: **********
    
  worker2:
    image: juicedata/ssh
    container_name: worker2
    restart: unless-stopped
    networks:
      static-network:
        ipv4_address: **********
  
networks:
  static-network:
    ipam:
      config:
        - subnet: **********/16
