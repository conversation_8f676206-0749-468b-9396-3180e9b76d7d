<configuration>
	<property>
		<name>fs.contract.test.fs.jfs</name>
		<value>jfs:///</value>
	</property>
	<property>
		<name>fs.jfs.impl</name>
		<value>io.juicefs.JuiceFileSystem</value>
	</property>
	<property>
		<name>juicefs.no-usage-report</name>
		<value>true</value>
	</property>	
	<property>
		<name>juicefs.names</name>
		<value>a.local,b.local,c.local,d.local,e.local</value>
	</property>
	<property>
		<name>juicefs.hosts</name>
		<value>*********,*********,*********,*********,*********</value>
	</property>
	<property>
		<name>fs.contract.test.root-tests-enabled</name>
		<value>true</value>
	</property>
	<property>
		<name>fs.contract.is-case-sensitive</name>
		<value>true</value>
	</property>
	<property>
		<name>fs.contract.supports-append</name>
		<value>true</value>
	</property>
	<property>
		<name>fs.contract.supports-atomic-directory-delete</name>
		<value>true</value>
	</property>
	<property>
		<name>fs.contract.supports-block-locality</name>
		<value>true</value>
	</property>
	<property>
		<name>fs.contract.supports-atomic-rename</name>
		<value>true</value>
	</property>
	<property>
		<name>fs.contract.supports-settimes</name>
		<value>true</value>
	</property>
	<property>
		<name>fs.contract.supports-getfilestatus</name>
		<value>true</value>
	</property>
	<property>
		<name>fs.contract.supports-concat</name>
		<value>true</value>
	</property>
	<property>
		<name>fs.contract.supports-seek</name>
		<value>true</value>
	</property>
	<property>
		<name>fs.contract.rejects-seek-past-eof</name>
		<value>true</value>
	</property>
	<property>
		<name>fs.contract.supports-strict-exceptions</name>
		<value>true</value>
	</property>
	<property>
		<name>fs.contract.supports-unix-permissions</name>
		<value>true</value>
	</property>
	<property>
		<name>fs.contract.rename-returns-false-if-dest-exists</name>
		<value>true</value>
	</property>
	<property>
		<name>fs.contract.supports-file-reference</name>
		<value>true</value>
	</property>
	<property>
		<name>fs.contract.rename-returns-false-if-source-missing</name>
		<value>true</value>
	</property>
</configuration>
