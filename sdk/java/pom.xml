<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>io.juicefs</groupId>
	<name>juicefs-hadoop</name>
	<url>https://github.com/juicedata/juicefs</url>
	<description>Hadoop FileSystem implementation for JuiceFS</description>
	<artifactId>juicefs-hadoop</artifactId>
	<version>1.2-dev</version>
	<packaging>jar</packaging>
	<properties>
		<hadoop.version>3.1.4</hadoop.version>
		<argLine>-Djdk.net.URLClassPath.disableClassPathURLCheck=true -Djava.library.path=${project.basedir}/../mount/libjfs:${java.library.path} -Djdk.attach.allowAttachSelf=true</argLine>
	</properties>

	<developers>
		<developer>
			<name>Juicedata</name>
			<email><EMAIL></email>
		</developer>
	</developers>

	<licenses>
		<license>
			<name>The Apache Software License, Version 2.0</name>
			<url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
			<distribution>repo</distribution>
		</license>
	</licenses>

	<scm>
		<url>https://github.com/juicedata/juicefs</url>
		<connection>https://github.com/juicedata/juicefs</connection>
		<developerConnection>scm:git:https://github.com/juicedata/juicefs</developerConnection>
	</scm>

	<build>
		<plugins>
			<plugin>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.19.1</version>
				<configuration>
					<argLine>${argLine}</argLine>
					<trimStackTrace>false</trimStackTrace>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<finalName>${artifactId}-${version}</finalName>
					<relocations>
						<relocation>
							<pattern>org.objectweb.asm</pattern>
							<shadedPattern>io.juicefs.shaded.org.objectweb.asm</shadedPattern>
						</relocation>
						<relocation>
							<pattern>com.beust</pattern>
							<shadedPattern>io.juicefs.shaded.com.beust</shadedPattern>
						</relocation>
						<relocation>
							<pattern>org.json</pattern>
							<shadedPattern>io.juicefs.shaded.org.json</shadedPattern>
						</relocation>
						<relocation>
							<pattern>org.javassist</pattern>
							<shadedPattern>io.juicefs.shaded.org.javassist</shadedPattern>
						</relocation>
						<relocation>
							<pattern>com.google.common</pattern>
							<shadedPattern>io.juicefs.shaded.com.google.common</shadedPattern>
						</relocation>
					</relocations>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>8</source>
					<target>8</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.sonatype.plugins</groupId>
				<artifactId>nexus-staging-maven-plugin</artifactId>
				<version>1.6.7</version>
				<extensions>true</extensions>
				<configuration>
					<serverId>ossrh</serverId>
					<nexusUrl>https://s01.oss.sonatype.org/</nexusUrl>
					<autoReleaseAfterClose>false</autoReleaseAfterClose>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>2.9.1</version>
				<configuration>
					<source>8</source>
				</configuration>
				<executions>
					<execution>
						<id>attach-javadocs</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>2.2.1</version>
				<configuration>
					<excludeResources>true</excludeResources>
				</configuration>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar-no-fork</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-gpg-plugin</artifactId>
				<version>1.6</version>
				<executions>
					<execution>
						<id>sign-artifacts</id>
						<phase>verify</phase>
						<goals>
							<goal>sign</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<!-- Prevent gpg from using pinentry programs -->
					<gpgArguments>
						<arg>--pinentry-mode</arg>
						<arg>loopback</arg>
					</gpgArguments>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<archive>
						<manifest>
							<addClasspath>true</addClasspath>
							<mainClass>io.juicefs.Main</mainClass>
						</manifest>
					</archive>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.7</version>
				<executions>
					<execution>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>report</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.pitest</groupId>
				<artifactId>pitest-maven</artifactId>
				<version>1.9.11</version>
				<configuration>
					<targetClasses>
						<param>io.juicefs.JuiceFileSystemImpl*</param>
					</targetClasses>
					<targetTests>
						<param>io.juicefs.JuiceFileSystemTest</param>
					</targetTests>
					<timeoutConstant>1000</timeoutConstant>
					<avoidCallsTo>
						<avoidCallsTo>org.apache.log4j</avoidCallsTo>
						<avoidCallsTo>org.slf4j</avoidCallsTo>
						<avoidCallsTo>org.apache.commons.logging</avoidCallsTo>
					</avoidCallsTo>
				</configuration>
			</plugin>
		</plugins>
		<resources>
			<resource>
				<directory>libjfs/target</directory>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
			</resource>
		</resources>
		<testResources>
			<testResource>
				<directory>conf</directory>
			</testResource>
		</testResources>
	</build>
	<dependencies>
		<dependency>
			<groupId>com.github.jnr</groupId>
			<artifactId>jnr-ffi</artifactId>
			<version>2.2.12</version>
		</dependency>
		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-common</artifactId>
			<version>${hadoop.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-common</artifactId>
			<version>${hadoop.version}</version>
			<scope>test</scope>
			<type>test-jar</type>
		</dependency>
		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-client</artifactId>
			<version>${hadoop.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.13.1</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20180813</version>
		</dependency>
		<dependency>
			<groupId>org.javassist</groupId>
			<artifactId>javassist</artifactId>
			<version>3.25.0-GA</version>
		</dependency>
		<dependency>
			<groupId>com.beust</groupId>
			<artifactId>jcommander</artifactId>
			<version>1.81</version>
		</dependency>
		<dependency>
			<groupId>org.apache.hive</groupId>
			<artifactId>hive-metastore</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.apache.hadoop</groupId>
					<artifactId>hadoop-annotations</artifactId>
				</exclusion>
			</exclusions>
			<scope>
				provided
			</scope>
			<version>1.2.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-hadoop-fs</artifactId>
			<version>1.10.3</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-core</artifactId>
			<version>1.10.3</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>32.0.1-jre</version>
		</dependency>
		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-minicluster</artifactId>
			<version>${hadoop.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.flink</groupId>
			<artifactId>flink-streaming-java_2.12</artifactId>
			<version>1.10.0</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.kitesdk</groupId>
			<artifactId>kite-data-core</artifactId>
			<version>1.1.0</version>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>

	<distributionManagement>
		<snapshotRepository>
			<id>ossrh</id>
			<url>https://s01.oss.sonatype.org/content/repositories/snapshots</url>
		</snapshotRepository>
		<repository>
			<id>ossrh</id>
			<url>https://s01.oss.sonatype.org/service/local/staging/deploy/maven2/</url>
		</repository>
	</distributionManagement>
</project>
