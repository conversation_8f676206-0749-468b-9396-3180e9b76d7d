<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="753px" height="283px" viewBox="-0.5 -0.5 753 283" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2023-10-11T06:01:59.649Z&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/22.0.2 Chrome/114.0.5735.289 Electron/25.8.4 Safari/537.36&quot; version=&quot;22.0.2&quot; etag=&quot;fuyxA5ZPuPFpzW7GHeHa&quot; type=&quot;device&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;MISeii3dosJveUr4_lzj&quot;&gt;7VlBl5s2EP41Pu4+JAHGR69301zS9nUPTY8yCKNUIEeItd1f35ERYEB+2byA0yZ7QhqNRtL3zYw18oJs8uMviu6zDzJhYoG95LggjwuMUeT58DGSUy0JV6tasFM8sUqd4Jn/w6zQs9KKJ6zsKWopheb7vjCWRcFi3ZNRpeShr5ZK0V91T3dsJHiOqRhL/+SJzmppFHid/D3ju6xZGXl2JKeNshWUGU3k4UJEnhZko6TUdSs/bpgw4DW41PPeXRltN6ZYoV8zAdcTXqio7NnsvvSpOaySVZEwo48W5OGQcc2e9zQ2owegF2SZzoUdTrkQGymkgn4hC1B6SGiZtdNLreTfLWiklTRzFpisVxv/6QFG7NaY0ux49XioBQ28jcmcaXUCFTuh9RjraNi3/UNHG2loyy4ow5EVUusqu9Z2hyY0LKBucMnXgOt9HbgAVJqmOI5dECbhNgzCaSAkYR9CH40hRKEDwikQ9B0IhkIbLCRs/xLK8HMlm4G78pwy1qAARz2egWjGobUz39+2n0xqwN6zlsrEuzW8VY1GI4FN1qs14gGHgK3uE9UnxMaBIzSo4LsCujFwwUD+YJjikGfWdiDnSWKWcXpG33emoHo5oJoEY6p9fB+MySYTkB1cJbvc0+KbyIYvzQ1mxbY0nz/Yjktj8pLkepUfnmQU9FNi4Ihn3xHPU1AczpsRE8qi1JkRwzhi23SejEi8G2bE5XwZ8QPTNKGavmVCyITL+36YEOzIhXimTBjNHCYBixLfFSYR3pJwoovD8O5FHHcvhGYKk9V8YTIwlNagd3obWSkOPoy9X9lhPP1TxWOWlmYLpyK+Glk/fQiCw9wvBzEYjmMwmu060rjmj1ob3aHVl4sj7ArQSSIUoXmT3G2qozsU9n30puURcpXvb/XRXCnpDgUDsm9bICHXg8LcFdL6p6uQRjTftERCrjeP/12NNMqLNy2S0PWnhLcqacpA8b9rmYRe8ZxQZnRvmqlgx7V5+oejsyKxzcdY0LLkdThQpcfiC5b6+LEj1x/tiGn/ZeSARt17PF6oPZ5sZ3hFiWJ2vqIA3ur0sZlhOrU1HDT9zt651xgcxfA2Cvygo5clgz8ySihO4kZkMw0cfMea6Hu1D1wS7DniuBUqJqjmL/19uGi3a/wu+TlEG6/E/Vuqvxx4Tn0kO6tznpEhPDBEhoZqHEaGzl7YHvx1jvntrzRodSX/XD5derHg7GyPxjErTVGZt+nJK5l6gVrT7IOb/r7aCvBp2KbJHgVQjjdGTYDLG6Fp5/R8EHBUZhWL0nAHQ1xXwKMsyhnzXMJSWp2N/8dS3eAtww/Hv2YET3MjgG73/1/tfN2/qOTpXw==&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><rect x="401" y="1" width="350" height="280" rx="42" ry="42" fill="none" stroke="#a9c4eb" stroke-width="3" stroke-dasharray="9 9" pointer-events="all"/><rect x="551" y="171" width="160" height="80" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><rect x="561" y="196" width="142.5" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 141px; height: 1px; padding-top: 211px; margin-left: 562px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Object Storage<br /></font></div></div></div></foreignObject><text x="632" y="215" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Object Storage&#xa;</text></switch></g><rect x="506" y="11" width="140" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 26px; margin-left: 507px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;"> Region B</span></div></div></div></foreignObject><text x="576" y="30" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle"> Region B</text></switch></g><rect x="551" y="61" width="160" height="80" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><rect x="568.5" y="86" width="125" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 123px; height: 1px; padding-top: 101px; margin-left: 570px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Metadata<br /></font></div></div></div></foreignObject><text x="631" y="105" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Metadata&#xa;</text></switch></g><rect x="401" y="101" width="110" height="80" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/><rect x="414.75" y="126" width="82.5" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 81px; height: 1px; padding-top: 141px; margin-left: 416px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;"><font face="Courier New">juicefs sync</font><br /></font></div></div></div></foreignObject><text x="456" y="145" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">juicefs sync&#xa;</text></switch></g><rect x="1" y="1" width="210" height="280" rx="31.5" ry="31.5" fill="none" stroke="#a9c4eb" stroke-width="3" stroke-dasharray="9 9" pointer-events="all"/><rect x="26" y="171" width="160" height="80" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><rect x="36" y="196" width="142.5" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 141px; height: 1px; padding-top: 211px; margin-left: 37px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Object Storage<br /></font></div></div></div></foreignObject><text x="107" y="215" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Object Storage&#xa;</text></switch></g><rect x="36" y="11" width="140" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 26px; margin-left: 37px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;"> Region A</span></div></div></div></foreignObject><text x="106" y="30" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle"> Region A</text></switch></g><rect x="26" y="61" width="160" height="80" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><rect x="43.5" y="86" width="125" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 123px; height: 1px; padding-top: 101px; margin-left: 45px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Metadata<br /></font></div></div></div></foreignObject><text x="106" y="105" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Metadata&#xa;</text></switch></g><path d="M 204.95 107.78 L 203.98 118.24 L 186.5 101.05 L 206.85 87.37 L 205.88 97.83 L 382.05 114.22 L 383.02 103.76 L 400.5 120.95 L 380.15 134.63 L 381.12 124.17 Z" fill="#f8cecc" stroke="#b85450" stroke-miterlimit="10" pointer-events="all"/><rect x="201" y="221" width="320" height="30" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 318px; height: 1px; padding-top: 236px; margin-left: 202px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 19px;">Region B client access metadata service via public internet, slow in small file intensive situations</font></div></div></div></foreignObject><text x="361" y="240" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Region B client access metadata service via public in...</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>