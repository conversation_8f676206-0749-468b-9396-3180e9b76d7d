<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="596px" height="484px" viewBox="-0.5 -0.5 596 484" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2023-08-23T03:57:25.591Z&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/21.6.8 Chrome/114.0.5735.289 Electron/25.5.0 Safari/537.36&quot; version=&quot;21.6.8&quot; etag=&quot;Cr_4rKImU3eyL6xkLIlj&quot; type=&quot;device&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;Yj85N8VVIfPV-Kh8y3EX&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs><style type="text/css">@import url(https://fonts.googleapis.com/css?family=Architects+Daughter);&#xa;</style></defs><g><rect x="173" y="1" width="240" height="480" rx="36" ry="36" fill="none" stroke="#b9e0a5" stroke-width="3" stroke-dasharray="9 9" pointer-events="all"/><path d="M 73 221 L 227.6 124.38" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 232.05 121.59 L 227.97 128.27 L 227.6 124.38 L 224.26 122.33 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 172px; margin-left: 148px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;"><font face="Courier New" style="font-size: 20px;">write</font></div></div></div></foreignObject><text x="148" y="175" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">write</text></switch></g><rect x="233" y="91" width="120" height="60" fill="#f8cecc" stroke="#b85450" pointer-events="all"/><rect x="263" y="106" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 121px; margin-left: 264px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">pending slice</font></div></div></div></foreignObject><text x="293" y="125" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">pending sl...</text></switch></g><ellipse cx="48" cy="218.5" rx="7.5" ry="7.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><path d="M 48 226 L 48 251 M 48 231 L 33 231 M 48 231 L 63 231 M 48 251 L 33 271 M 48 251 L 63 271" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 278px; margin-left: 48px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;"><font style="font-size: 20px;">Application</font></div></div></div></foreignObject><text x="48" y="290" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Appli...</text></switch></g><path d="M 353 121 L 466.63 121" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 471.88 121 L 464.88 124.5 L 466.63 121 L 464.88 117.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 121px; margin-left: 408px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;"><font face="Courier New" style="font-size: 20px;">flush</font></div></div></div></foreignObject><text x="408" y="125" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">flush</text></switch></g><rect x="473" y="1" width="120" height="480" rx="18" ry="18" fill="none" stroke="#b9e0a5" stroke-width="3" stroke-dasharray="9 9" pointer-events="all"/><rect x="503" y="21" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 36px; margin-left: 504px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;">Object Storage</span></div></div></div></foreignObject><text x="533" y="40" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Object Sto...</text></switch></g><rect x="498" y="71" width="70" height="20" fill="#ffe6cc" stroke="#d79b00" pointer-events="all"/><rect x="498" y="101" width="70" height="20" fill="#ffe6cc" stroke="#d79b00" pointer-events="all"/><rect x="498" y="131" width="70" height="20" fill="#ffe6cc" stroke="#d79b00" pointer-events="all"/><rect x="498" y="161" width="70" height="20" fill="#ffe6cc" stroke="#d79b00" pointer-events="all"/><path d="M 473 121 L 498 71" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 473 121 L 498 181" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><rect x="223" y="191" width="330" height="30" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 328px; height: 1px; padding-top: 206px; margin-left: 224px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 16px;"><font face="Courier New">write</font> only commit changes to client memory buffer, and is not persisted until <font face="Courier New">flush</font></font></div></div></div></foreignObject><text x="388" y="210" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">write only commit changes to client memory buffer, and...</text></switch></g><path d="M 173 241 L 413 241" fill="none" stroke="#b9e0a5" stroke-width="3" stroke-miterlimit="10" stroke-dasharray="9 9" pointer-events="stroke"/><path d="M 233 281 L 79.26 252.17" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 74.1 251.21 L 81.62 249.06 L 79.26 252.17 L 80.33 255.94 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 266px; margin-left: 148px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;"><font face="Courier New" style="font-size: 20px;">read</font></div></div></div></foreignObject><text x="148" y="270" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">read</text></switch></g><rect x="498" y="251" width="70" height="20" fill="#ffe6cc" stroke="#d79b00" pointer-events="all"/><rect x="498" y="291" width="70" height="20" fill="#ffe6cc" stroke="#d79b00" pointer-events="all"/><rect x="498" y="351" width="70" height="20" fill="#ffe6cc" stroke="#d79b00" pointer-events="all"/><rect x="498" y="391" width="70" height="20" fill="#ffe6cc" stroke="#d79b00" pointer-events="all"/><rect x="213" y="431" width="360" height="30" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 358px; height: 1px; padding-top: 446px; margin-left: 214px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 16px;">Upon sequential read, JuiceFS Client performs readahead, to improve performance</span></div></div></div></foreignObject><text x="393" y="450" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Upon sequential read, JuiceFS Client performs readahead, to...</text></switch></g><rect x="233" y="261" width="120" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 281px; margin-left: 264px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">slice</font></div></div></div></foreignObject><text x="293" y="285" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">slice</text></switch></g><rect x="233" y="361" width="120" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 381px; margin-left: 249px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">next slice</font></div></div></div></foreignObject><text x="293" y="385" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">next slice</text></switch></g><path d="M 293 301 L 293 354.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 293 359.88 L 289.5 352.88 L 293 354.63 L 296.5 352.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 326px; margin-left: 293px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;"><font face="Courier New" style="font-size: 20px;">readahead</font></div></div></div></foreignObject><text x="293" y="330" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">readahead</text></switch></g><path d="M 472.64 281.32 L 359.37 281.02" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 354.12 281 L 361.13 277.52 L 359.37 281.02 L 361.11 284.52 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 282px; margin-left: 408px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;"><font face="Courier New" style="font-size: 20px;">GET</font></div></div></div></foreignObject><text x="408" y="285" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">GET</text></switch></g><path d="M 472.88 280.36 L 498 251" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 473.12 280.84 L 498 311" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 472 380 L 359.37 380.95" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><path d="M 354.12 380.99 L 361.09 377.43 L 359.37 380.95 L 361.15 384.43 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 382px; margin-left: 413px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;"><font style=""><font face="Courier New" style="font-size: 20px;">GET</font><br /><span style="font-size: 14px;">(readahead)</span><br /></font></div></div></div></foreignObject><text x="413" y="385" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">GET...</text></switch></g><path d="M 472 379 L 498 351" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><path d="M 473 380 L 498 411" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 36px; margin-left: 264px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;">Read/Write<br />Buffer</span></div></div></div></foreignObject><text x="293" y="40" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Read/Write...</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>