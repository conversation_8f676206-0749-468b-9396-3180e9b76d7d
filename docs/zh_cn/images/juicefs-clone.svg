<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="921px" height="522px" viewBox="-0.5 -0.5 921 522" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2023-08-17T10:09:03.798Z&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/21.6.8 Chrome/114.0.5735.289 Electron/25.5.0 Safari/537.36&quot; version=&quot;21.6.8&quot; etag=&quot;R4eDpEzIFI9DANx392rl&quot; type=&quot;device&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;mPtkEVMkKTPVAu7l3zLB&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><path d="M 548 4.85 C 548 2.72 549.57 1 551.5 1 L 558.5 1 C 560.43 1 562 2.72 562 4.85 L 614.5 4.85 C 616.43 4.85 618 6.57 618 8.69 L 618 47.15 C 618 49.28 616.43 51 614.5 51 L 551.5 51 C 549.57 51 548 49.28 548 47.15 Z M 548 8.69 L 618 8.69" fill="none" stroke="#0080f0" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 26px; margin-left: 564px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Verdana; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">/jfs</div></div></div></foreignObject><text x="583" y="31" fill="rgb(0, 0, 0)" font-family="Verdana" font-size="16px" text-anchor="middle">/jfs</text></switch></g><path d="M 370.08 335 L 436.62 273" fill="none" stroke="#4d4d4d" stroke-miterlimit="10" pointer-events="none"/><path d="M 346.33 340.76 C 346.68 337.72 349.06 335.33 352.06 335 L 383.97 335 L 393.83 344.95 L 393.83 378.79 C 393.66 382.12 391.06 384.79 387.78 385 L 352.06 385 C 349.1 384.68 346.74 382.37 346.33 379.38 Z M 350.21 378.48 C 350.21 379.69 351.05 380.72 352.21 380.96 L 387.86 380.96 C 388.99 380.7 389.8 379.67 389.8 378.48 L 389.8 347.27 L 381.83 347.27 L 381.83 339.09 L 352.21 339.09 C 351.14 339.36 350.37 340.3 350.31 341.41 Z M 358.78 351.01 L 380.78 351.01 L 380.78 355.15 L 358.78 355.15 Z M 358.78 358.89 L 380.78 358.89 L 380.78 363.03 L 358.78 363.03 Z M 358.78 366.77 L 380.78 366.77 L 380.78 370.96 L 358.78 370.96 Z" fill="#808080" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 392px; margin-left: 370px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">file3</div></div></div></foreignObject><text x="370" y="408" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">file3</text></switch></g><path d="M 331.75 232 L 370.48 162" fill="none" stroke="#4d4d4d" stroke-miterlimit="10" pointer-events="none"/><path d="M 289 232.76 C 289.35 229.72 291.73 227.33 294.73 227 L 326.64 227 L 336.5 236.95 L 336.5 270.79 C 336.33 274.12 333.73 276.79 330.45 277 L 294.73 277 C 291.77 276.68 289.41 274.37 289 271.38 Z M 292.88 270.48 C 292.88 271.69 293.72 272.72 294.88 272.96 L 330.53 272.96 C 331.66 272.7 332.47 271.67 332.47 270.48 L 332.47 239.27 L 324.5 239.27 L 324.5 231.09 L 294.88 231.09 C 293.81 231.36 293.04 232.3 292.98 233.41 Z M 301.45 243.01 L 323.45 243.01 L 323.45 247.15 L 301.45 247.15 Z M 301.45 250.89 L 323.45 250.89 L 323.45 255.03 L 301.45 255.03 Z M 301.45 258.77 L 323.45 258.77 L 323.45 262.96 L 301.45 262.96 Z" fill="#808080" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 284px; margin-left: 313px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">file1</div></div></div></foreignObject><text x="313" y="300" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">file1</text></switch></g><path d="M 403.65 231.96 C 403.65 231.04 404 230.16 404.63 229.51 C 405.26 228.86 406.12 228.5 407.01 228.5 L 413.72 228.5 C 414.61 228.5 415.47 228.86 416.1 229.51 C 416.73 230.16 417.08 231.04 417.08 231.96 L 467.44 231.96 C 469.3 231.96 470.8 233.51 470.8 235.42 L 470.8 270.04 C 470.8 270.96 470.45 271.84 469.82 272.49 C 469.19 273.14 468.33 273.5 467.44 273.5 L 407.01 273.5 C 406.12 273.5 405.26 273.14 404.63 272.49 C 404 271.84 403.65 270.96 403.65 270.04 Z M 403.65 235.42 L 470.8 235.42" fill="none" stroke="#0080f0" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 62px; height: 1px; padding-top: 252px; margin-left: 406px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Verdana; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">d2</div></div></div></foreignObject><text x="437" y="257" fill="rgb(0, 0, 0)" font-family="Verdana" font-size="16px" text-anchor="middle">d2</text></switch></g><path d="M 373.3 117.49 L 580.27 51.1" fill="none" stroke="#4d4d4d" stroke-miterlimit="10" pointer-events="none"/><path d="M 436.62 233 L 369.47 161.01" fill="none" stroke="#4d4d4d" stroke-miterlimit="10" pointer-events="none"/><path d="M 336.5 120.46 C 336.5 119.54 336.85 118.66 337.48 118.01 C 338.11 117.36 338.97 117 339.86 117 L 346.57 117 C 347.46 117 348.32 117.36 348.95 118.01 C 349.58 118.66 349.93 119.54 349.93 120.46 L 400.29 120.46 C 402.15 120.46 403.65 122.01 403.65 123.92 L 403.65 158.54 C 403.65 159.46 403.3 160.34 402.67 160.99 C 402.04 161.64 401.18 162 400.29 162 L 339.86 162 C 338.97 162 338.11 161.64 337.48 160.99 C 336.85 160.34 336.5 159.46 336.5 158.54 Z M 336.5 123.92 L 403.65 123.92" fill="none" stroke="#0080f0" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 62px; height: 1px; padding-top: 139px; margin-left: 339px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Verdana; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">d1</div></div></div></foreignObject><text x="370" y="144" fill="rgb(0, 0, 0)" font-family="Verdana" font-size="16px" text-anchor="middle">d1</text></switch></g><path d="M 815 325 L 884.97 274" fill="none" stroke="#4d4d4d" stroke-miterlimit="10" pointer-events="none"/><path d="M 773.25 232 L 811.98 162.5" fill="none" stroke="#4d4d4d" stroke-miterlimit="10" pointer-events="none"/><path d="M 730.5 232.76 C 730.85 229.72 733.23 227.33 736.23 227 L 768.14 227 L 778 236.95 L 778 270.79 C 777.83 274.12 775.23 276.79 771.95 277 L 736.23 277 C 733.27 276.68 730.91 274.37 730.5 271.38 Z M 734.38 270.48 C 734.38 271.69 735.22 272.72 736.38 272.96 L 772.03 272.96 C 773.16 272.7 773.97 271.67 773.97 270.48 L 773.97 239.27 L 766 239.27 L 766 231.09 L 736.38 231.09 C 735.31 231.36 734.54 232.3 734.48 233.41 Z M 742.95 243.01 L 764.95 243.01 L 764.95 247.15 L 742.95 247.15 Z M 742.95 250.89 L 764.95 250.89 L 764.95 255.03 L 742.95 255.03 Z M 742.95 258.77 L 764.95 258.77 L 764.95 262.96 L 742.95 262.96 Z" fill="#808080" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 284px; margin-left: 754px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">file1</div></div></div></foreignObject><text x="754" y="300" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">file1</text></switch></g><path d="M 852 232.96 C 852 232.04 852.35 231.16 852.98 230.51 C 853.61 229.86 854.47 229.5 855.36 229.5 L 862.07 229.5 C 863.93 229.5 865.43 231.05 865.43 232.96 L 915.79 232.96 C 917.65 232.96 919.15 234.51 919.15 236.42 L 919.15 271.04 C 919.15 271.96 918.8 272.84 918.17 273.49 C 917.54 274.14 916.68 274.5 915.79 274.5 L 855.36 274.5 C 854.47 274.5 853.61 274.14 852.98 273.49 C 852.35 272.84 852 271.96 852 271.04 Z M 852 236.42 L 919.15 236.42" fill="none" stroke="#0080f0" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 62px; height: 1px; padding-top: 252px; margin-left: 854px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Verdana; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">d2</div></div></div></foreignObject><text x="886" y="257" fill="rgb(0, 0, 0)" font-family="Verdana" font-size="16px" text-anchor="middle">d2</text></switch></g><path d="M 884.97 234 L 810.97 161.51" fill="none" stroke="#4d4d4d" stroke-miterlimit="10" pointer-events="none"/><path d="M 778 120.96 C 778 120.04 778.35 119.16 778.98 118.51 C 779.61 117.86 780.47 117.5 781.36 117.5 L 788.07 117.5 C 788.96 117.5 789.82 117.86 790.45 118.51 C 791.08 119.16 791.43 120.04 791.43 120.96 L 841.79 120.96 C 843.65 120.96 845.15 122.51 845.15 124.42 L 845.15 159.04 C 845.15 159.96 844.8 160.84 844.17 161.49 C 843.54 162.14 842.68 162.5 841.79 162.5 L 781.36 162.5 C 780.47 162.5 779.61 162.14 778.98 161.49 C 778.35 160.84 778 159.96 778 159.04 Z M 778 124.42 L 845.15 124.42" fill="none" stroke="#0080f0" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 63px; height: 1px; padding-top: 143px; margin-left: 782px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Verdana; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">d1.bak</div></div></div></foreignObject><text x="814" y="148" fill="rgb(0, 0, 0)" font-family="Verdana" font-size="16px" text-anchor="middle">d1.bak</text></switch></g><path d="M 811.24 118.81 L 580.83 51" fill="none" stroke="#4d4d4d" stroke-miterlimit="10" pointer-events="none"/><rect x="410.08" y="481" width="10" height="40" fill="#ffcc99" stroke="#36393d" transform="rotate(180,415.08,501)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 12px; height: 40px; padding-top: 481px; margin-left: 409px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; width: 10px; height: 40px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; width: 100%; height: 100%; white-space: normal; overflow-wrap: normal;"><p style="text-align: left; margin: 13px;"><br /></p></div></div></div></foreignObject><text x="415" y="505" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle"></text></switch></g><rect x="443.08" y="481" width="10" height="40" fill="#ffcc99" stroke="#36393d" transform="rotate(180,448.08,501)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 12px; height: 40px; padding-top: 481px; margin-left: 442px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; width: 10px; height: 40px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; width: 100%; height: 100%; white-space: normal; overflow-wrap: normal;"><p style="text-align: left; margin: 13px;"><br /></p></div></div></div></foreignObject><text x="448" y="505" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle"></text></switch></g><rect x="739.08" y="481" width="10" height="40" fill="#ffcc99" stroke="#36393d" transform="rotate(180,744.08,501)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 12px; height: 40px; padding-top: 481px; margin-left: 738px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; width: 10px; height: 40px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; width: 100%; height: 100%; white-space: normal; overflow-wrap: normal;"><p style="text-align: left; margin: 13px;"><br /></p></div></div></div></foreignObject><text x="744" y="505" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle"></text></switch></g><rect x="771.93" y="481" width="10" height="40" fill="#ffcc99" stroke="#36393d" transform="rotate(180,776.93,501)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 12px; height: 40px; padding-top: 481px; margin-left: 771px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; width: 10px; height: 40px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; width: 100%; height: 100%; white-space: normal; overflow-wrap: normal;"><p style="text-align: left; margin: 13px;"><br /></p></div></div></div></foreignObject><text x="777" y="505" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle"></text></switch></g><path d="M 836.85 373 L 781.93 481" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 6" pointer-events="none"/><path d="M 391.93 383 L 781.93 481" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 6" pointer-events="none"/><rect x="476.08" y="481" width="10" height="40" fill="#ffcc99" stroke="#36393d" transform="rotate(180,481.08,501)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 12px; height: 40px; padding-top: 481px; margin-left: 475px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; width: 10px; height: 40px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; width: 100%; height: 100%; white-space: normal; overflow-wrap: normal;"><p style="text-align: left; margin: 13px;"><br /></p></div></div></div></foreignObject><text x="481" y="505" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle"></text></switch></g><rect x="509.08" y="481" width="10" height="40" fill="#ffcc99" stroke="#36393d" transform="rotate(180,514.08,501)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 12px; height: 40px; padding-top: 481px; margin-left: 508px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; width: 10px; height: 40px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; width: 100%; height: 100%; white-space: normal; overflow-wrap: normal;"><p style="text-align: left; margin: 13px;"><br /></p></div></div></div></foreignObject><text x="514" y="505" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle"></text></switch></g><rect x="542.08" y="481" width="10" height="40" fill="#ffcc99" stroke="#36393d" transform="rotate(180,547.08,501)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 12px; height: 40px; padding-top: 481px; margin-left: 541px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; width: 10px; height: 40px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; width: 100%; height: 100%; white-space: normal; overflow-wrap: normal;"><p style="text-align: left; margin: 13px;"><br /></p></div></div></div></foreignObject><text x="547" y="505" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle"></text></switch></g><rect x="574.08" y="481" width="10" height="40" fill="#ffcc99" stroke="#36393d" transform="rotate(180,579.08,501)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 12px; height: 40px; padding-top: 481px; margin-left: 573px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; width: 10px; height: 40px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; width: 100%; height: 100%; white-space: normal; overflow-wrap: normal;"><p style="text-align: left; margin: 13px;"><br /></p></div></div></div></foreignObject><text x="579" y="505" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle"></text></switch></g><rect x="607.08" y="481" width="10" height="40" fill="#ffcc99" stroke="#36393d" transform="rotate(180,612.08,501)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 12px; height: 40px; padding-top: 481px; margin-left: 606px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; width: 10px; height: 40px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; width: 100%; height: 100%; white-space: normal; overflow-wrap: normal;"><p style="text-align: left; margin: 13px;"><br /></p></div></div></div></foreignObject><text x="612" y="505" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle"></text></switch></g><rect x="640.08" y="481" width="10" height="40" fill="#ffcc99" stroke="#36393d" transform="rotate(180,645.08,501)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 12px; height: 40px; padding-top: 481px; margin-left: 639px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; width: 10px; height: 40px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; width: 100%; height: 100%; white-space: normal; overflow-wrap: normal;"><p style="text-align: left; margin: 13px;"><br /></p></div></div></div></foreignObject><text x="645" y="505" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle"></text></switch></g><rect x="673.08" y="481" width="10" height="40" fill="#ffcc99" stroke="#36393d" transform="rotate(180,678.08,501)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 12px; height: 40px; padding-top: 481px; margin-left: 672px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; width: 10px; height: 40px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; width: 100%; height: 100%; white-space: normal; overflow-wrap: normal;"><p style="text-align: left; margin: 13px;"><br /></p></div></div></div></foreignObject><text x="678" y="505" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle"></text></switch></g><rect x="706.08" y="481" width="10" height="40" fill="#ffcc99" stroke="#36393d" transform="rotate(180,711.08,501)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 12px; height: 40px; padding-top: 481px; margin-left: 705px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; width: 10px; height: 40px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; width: 100%; height: 100%; white-space: normal; overflow-wrap: normal;"><p style="text-align: left; margin: 13px;"><br /></p></div></div></div></foreignObject><text x="711" y="505" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle"></text></switch></g><path d="M 348.23 383 L 410.08 481" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 6" pointer-events="none"/><path d="M 791.25 330.76 C 791.6 327.72 793.98 325.33 796.98 325 L 828.89 325 L 838.75 334.95 L 838.75 368.79 C 838.58 372.12 835.98 374.79 832.7 375 L 796.98 375 C 794.02 374.68 791.66 372.37 791.25 369.38 Z M 795.13 368.48 C 795.13 369.69 795.97 370.72 797.13 370.96 L 832.78 370.96 C 833.91 370.7 834.72 369.67 834.72 368.48 L 834.72 337.27 L 826.75 337.27 L 826.75 329.09 L 797.13 329.09 C 796.06 329.36 795.29 330.3 795.23 331.41 Z M 803.7 341.01 L 825.7 341.01 L 825.7 345.15 L 803.7 345.15 Z M 803.7 348.89 L 825.7 348.89 L 825.7 353.03 L 803.7 353.03 Z M 803.7 356.77 L 825.7 356.77 L 825.7 360.96 L 803.7 360.96 Z" fill="#808080" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 382px; margin-left: 815px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;"><span style="background-color: rgb(255, 255, 255);">file3</span></div></div></div></foreignObject><text x="815" y="398" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">file3</text></switch></g><path d="M 793.15 373 L 410.08 481" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 6" pointer-events="none"/><rect x="479" y="193" width="208" height="30" fill="rgb(255, 255, 255)" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 206px; height: 1px; padding-top: 208px; margin-left: 480px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;"><font face="Courier New">juicefs clone</font> <br />only copies metadata</span></div></div></div></foreignObject><text x="583" y="212" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">juicefs clone...</text></switch></g><rect x="470.8" y="335" width="245" height="30" fill="rgb(255, 255, 255)" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 243px; height: 1px; padding-top: 350px; margin-left: 472px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;">Underlying object storage data is not changed</span></div></div></div></foreignObject><text x="593" y="354" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Underlying object storage data is not cha...</text></switch></g><path d="M 429.5 160 L 429.5 150 L 742.35 149.69 L 742.33 139.19 L 761.35 154.67 L 742.37 170.19 L 742.35 159.69 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 380px; height: 1px; padding-top: 131px; margin-left: 387px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font face="Courier New" style="font-size: 20px;">juicefs clone d1 d1.bak</font></div></div></div></foreignObject><text x="577" y="135" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">juicefs clone d1 d1.bak</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 271px; height: 1px; padding-top: 140px; margin-left: 50px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 30px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Metadata Engine</div></div></div></foreignObject><text x="185" y="149" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="30px" text-anchor="middle">Metadata Engine</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 271px; height: 1px; padding-top: 498px; margin-left: 50px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 30px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Object Storage</div></div></div></foreignObject><text x="185" y="507" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="30px" text-anchor="middle">Object Storage</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>