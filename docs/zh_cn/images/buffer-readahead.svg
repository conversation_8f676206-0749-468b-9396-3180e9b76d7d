<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="608px" height="205px" viewBox="-0.5 -0.5 608 205" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2023-08-23T03:30:05.460Z&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/21.6.8 Chrome/114.0.5735.289 Electron/25.5.0 Safari/537.36&quot; version=&quot;21.6.8&quot; etag=&quot;DSBJPW7rkrxL5x07aCPd&quot; type=&quot;device&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;_Tu8nFvg6glaGyacgQiC&quot;&gt;7VhNc5swEP01zLSHZEAYbB9jO2mn03Yyk0PbowyL0QQjR8ix3V/fFYhv/BXbmTbpJYanXWn1tG9XwbDH8/UnQRfhN+5DZBDTXxv2xCDEGpg9/FHIJkPc4TADZoL52qgEHthv0KCp0SXzIakZSs4jyRZ10ONxDJ6sYVQIvqqbBTyqr7qgM2gBDx6N2ugP5sswQweOWeKfgc3CfGXL1CNzmhtrIAmpz1cVyL417LHgXGZP8/UYIkVezkvmd7dltAhMQCwPcSCZwzONlnpvBnEjdB0FHGfAAOVG79p9WvJ84CpJz+QGDXA1PNdROY5PM/V7x4Xyh6clxsKoWlUA9RODjPHxy5J5cPeAT+OIQbrUiqWhLUAEXMy1OQ3xT3q+KurYQywBPVYa09iDPHDcbBZ7FobmudgGkbBWeCjnEQIWPiZS8EcY8wgjticxj0FtE6NpQDRisxhfPYwXEB89g5AM0+JGD8yZ76tlRquQSXhYUE+tuUIRICb4MvZB8W4WYakJYL317KwiI1BKwOcgxQZNtIOdZ9Wm0FX2vipzsrAJK/loa4xqGcyKqctMwQedLN2JY3ckToPo+oa7KKmcQZVug9g+hUHgtc4GR1xvANPgRAY1Yx2EuV2EuWcgrLdVacmCxicprSHZIOO4tBvzpWCg1PgdVm13pH67eAo4i/Kta4oQp6apodPKEMu5kKSc/ZKC2L9R/UsRFtEkYV6d9S4+wG81s71sCIioZM91v669add7ztLUyytRv0FHghnogbaqtqPcMT+vZklrTiSpmIFsTZRSW8R/ENvuJTrf0drL2thhjUsd5Fc6xbuU8sM46DQdMus5cLiiapckPVl5NdmjI+fEzDlUFf1/XxVbkpuYL1QJ6e+Z6HwqGRyvkr396dWEpe+OOzvbVLw0Ssvd0o4/PIKIIfp49MrHFIAXCb5ZNRYqQ9KccUaGM9nVQrdViaps+jtbq3ltDmppe6WVfKKsrixSd+FBkMCpiT88quzoy4xPkzCtM9auAoR8ic1P9XJtuf0c+KVsr01i5cBkXbWfbKpv9yAYbkad9q6altWB2oX9gDJXuet0XXVy7G+phrb9atUw//7wBppRq4c0/8c5lH7H3TPRGem3/nej47tR80vL++tLltktoYs3puEFGlPe7S7cmXq9QaMz2fY76UzEHZ6nNJJGaSw+0J1cGvG1/EacmZdf2u3bPw==&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs><style type="text/css">@import url(https://fonts.googleapis.com/css?family=Architects+Daughter);&#xa;</style></defs><g><rect x="307" y="100" width="300" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 298px; height: 1px; padding-top: 115px; margin-left: 308px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">For sequential reads, JuiceFS Client will perform readahead to increase read performance</font></div></div></div></foreignObject><text x="457" y="119" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">For sequential reads, JuiceFS Client will perform...</text></switch></g><rect x="7" y="0" width="600" height="60" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><rect x="232" y="15" width="150" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 30px; margin-left: 233px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;"><font face="Courier New">file</font></span></div></div></div></foreignObject><text x="307" y="34" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">file</text></switch></g><path d="M 7 90 L 100.63 90" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 105.88 90 L 98.88 93.5 L 100.63 90 L 98.88 86.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 91px; margin-left: 58px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;"><font face="Courier New" style="font-size: 20px;">read</font></div></div></div></foreignObject><text x="58" y="94" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">read</text></switch></g><path d="M 107 120 L 270.63 120" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 275.88 120 L 268.88 123.5 L 270.63 120 L 268.88 116.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 123px; margin-left: 188px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;"><font style=""><font face="Courier New" style="font-size: 20px;">readahead</font><br /><font style="font-size: 16px;">(kernel)</font><br /></font></div></div></div></foreignObject><text x="188" y="126" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">readahead...</text></switch></g><path d="M 107 120 L 107.2 61.26" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 277 180 L 560.63 180" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 565.88 180 L 558.88 183.5 L 560.63 180 L 558.88 176.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 183px; margin-left: 416px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;"><font style=""><font face="Courier New" style="font-size: 20px;">readahead</font><br /><font style="font-size: 16px;">(JuiceFS Client)</font><br /></font></div></div></div></foreignObject><text x="416" y="186" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">readahead...</text></switch></g><path d="M 276 180 L 275.8 61.98" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>