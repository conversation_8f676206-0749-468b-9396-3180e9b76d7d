<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="913px" height="301px" viewBox="-0.5 -0.5 913 301" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2023-10-11T06:02:18.731Z&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/22.0.2 Chrome/114.0.5735.289 Electron/25.8.4 Safari/537.36&quot; version=&quot;22.0.2&quot; etag=&quot;nmz2rImeSU7FaVn-YPxT&quot; type=&quot;device&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;qmsLsmkmjUEaF6lOz7hg&quot;&gt;7VpNt5s2EP01XvodJAHPXtp+r+kmbU+9aLqUkQClgFyQY9xf35ER33KPkxg7Td7CBzT6QLp35qIRnpFNWr7L6T5+LxlPZthh5Yy8zDBGC8eFi7acKou/XFaGKBfMNGoNW/EPN0bHWA+C8aLXUEmZKLHvGwOZZTxQPRvNc3nsNwtl0n/qnkZ8ZNgGNBlb/xBMxZV14Tmt/Wcuorh+MnJMTUrrxsZQxJTJY8dEXmdkk0upqru03PBEg1fjUvX76UJtM7GcZ+qaDrjq8IkmB7M2My91qheby0PGuG6PZmR9jIXi2z0NdO0R6AVbrNLEVIciSTYykTmUM5lBozWjRdx0L1Qu/2pAI42l7jPDZLXcuK9rqDFT47ni5cXloQY08DYuU67yEzQxHRqPMY6GXVM+trSRmra4QxleGCM1rhI1Y7dowo0B1A4u+Rxwnc8DF4AKwxAHgQ1C5u98z78NhMTvQ+iiMYTIt0B4CwRdC4J+ojQWEqbfhdL/+yDrinlxlowVNICllmcg6nq4i/T1191HLQ3Y2SqZ63g3A+/yukVtgUlWT6vNAw4BW9Unqk+IiQNLaNBERBkUA+CCg32tmRKgMytTkQrG9GOsntH3nVtQ/Tygmnhjql385I3JJjcg27tIdrGn2VeRDVeaasyyXaEvv/NISD1kl+TqKd89ycjrS6JniWfXEs+3oNifVhEZ5YvQqoh+sOC7cBpFJM4dFfF5OkV8zxVlVNE3JQQlfH7qhwnBFi3EEynhYuIw8fiCubYwWeAd8W+0cRjuvYhl74XQRGGynC5MBgOFFehtu4085AJ8GDu/8OO4+8eDCHhY6CmcsuBiZP3wIQgO8/Q8iEF/HIOLybYjtWt+r7nRvEl8/iM5cslUyRFC04rcfbKjOcEPTI+QLX1/y4+mkqQ5QQ9NkJDtQGHqDGn1w2VIczTY4N81RUK2M4//XY400sW7Jkno8lHCW5Z0Sz108CPTJDT1ccJd8qQ5eWCehCY8T9jC68p5RxU/0tNbtOjXyhN+aEJzxbFCEdO9vg0TXq70lzJYO8+YuX0JEloUonp70FyNzR2a+gDyUqgPpkbf/6ntsNKq9FJ2mr2c6kIGK/zQLXR66WLb7Vw69cjibPARr4DEPKhN5i0Lq4h4TZ9/NaXd0HQsodkYc55QJT71J2Ij0TzjNynOUVf2aTf+4uGBG1RLMp1aTxiNgwYDucOBKhxGA51dqln3dV52+ejl2k0qWl5QlLXG/lCILNJTh19Uq4teCHaq97LDy73M9erBc/fVkmDaTtq8uR0JkRxzyvS8dUeeiFRkMBg7k6tioSd6lz0w4yE9nAf/1vRqOdIrd2n5fIon2gjX4z5Grr5Qer5E5q6RK2csV/ibU6tmnK+VK+xMJVdQbP9cUTVv/6JCXv8F&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><rect x="561" y="1" width="350" height="280" rx="42" ry="42" fill="none" stroke="#a9c4eb" stroke-width="3" stroke-dasharray="9 9" pointer-events="all"/><rect x="711" y="171" width="160" height="80" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><rect x="721" y="196" width="142.5" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 141px; height: 1px; padding-top: 211px; margin-left: 722px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Object Storage<br /></font></div></div></div></foreignObject><text x="792" y="215" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Object Storage&#xa;</text></switch></g><rect x="666" y="11" width="140" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 26px; margin-left: 667px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;"> Region B</span></div></div></div></foreignObject><text x="736" y="30" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle"> Region B</text></switch></g><rect x="711" y="61" width="160" height="80" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><rect x="728.5" y="86" width="125" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 123px; height: 1px; padding-top: 101px; margin-left: 730px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Metadata<br /></font></div></div></div></foreignObject><text x="791" y="105" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Metadata&#xa;</text></switch></g><rect x="561" y="101" width="110" height="80" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/><rect x="574.75" y="126" width="82.5" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 81px; height: 1px; padding-top: 141px; margin-left: 576px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;"><font face="Courier New">juicefs sync</font><br /></font></div></div></div></foreignObject><text x="616" y="145" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">juicefs sync&#xa;</text></switch></g><rect x="1" y="1" width="430" height="280" rx="42" ry="42" fill="none" stroke="#a9c4eb" stroke-width="3" stroke-dasharray="9 9" pointer-events="all"/><rect x="31" y="171" width="160" height="80" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><rect x="41" y="196" width="142.5" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 141px; height: 1px; padding-top: 211px; margin-left: 42px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Object Storage<br /></font></div></div></div></foreignObject><text x="112" y="215" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Object Storage&#xa;</text></switch></g><rect x="191" y="11" width="140" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 26px; margin-left: 192px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;"> Region A</span></div></div></div></foreignObject><text x="261" y="30" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle"> Region A</text></switch></g><rect x="31" y="61" width="160" height="80" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><rect x="48.5" y="86" width="125" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 123px; height: 1px; padding-top: 101px; margin-left: 50px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Metadata<br /></font></div></div></div></foreignObject><text x="111" y="105" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Metadata&#xa;</text></switch></g><rect x="321" y="101" width="110" height="80" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/><rect x="334.75" y="126" width="82.5" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 81px; height: 1px; padding-top: 141px; margin-left: 336px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">S3 Gateway<br /></font></div></div></div></foreignObject><text x="376" y="145" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">S3 Gateway&#xa;</text></switch></g><path d="M 208.17 111.51 L 205.08 121.55 L 191.48 101.15 L 214.2 91.92 L 211.11 101.96 L 303.83 130.49 L 306.92 120.45 L 320.52 140.85 L 297.8 150.08 L 300.89 140.04 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="254.75" y="251" width="320" height="30" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 318px; height: 1px; padding-top: 266px; margin-left: 256px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 19px;">By using a gateway as data export endpoint, metadata overhead is eliminated within Region A</span></div></div></div></foreignObject><text x="415" y="270" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">By using a gateway as data export endpoint, metadata...</text></switch></g><path d="M 450.5 146 L 450.5 156.5 L 431.5 141 L 450.5 125.5 L 450.5 136 L 541.5 136 L 541.5 125.5 L 560.5 141 L 541.5 156.5 L 541.5 146 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>