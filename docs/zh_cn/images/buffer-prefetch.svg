<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="601px" height="225px" viewBox="-0.5 -0.5 601 225" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2023-08-23T03:44:40.505Z&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/21.6.8 Chrome/114.0.5735.289 Electron/25.5.0 Safari/537.36&quot; version=&quot;21.6.8&quot; etag=&quot;s8aaSK0hE5cQFR-uBAba&quot; type=&quot;device&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;rkeZIHCDOp5ms1zvOI8w&quot;&gt;7VlNc9owEP01HJvBNrbhGEjSTKftZIZD26OwF6xGWFQWAfrru0Lyl2wnNDGZdNILlp6s1Wr37WotBt5svf8oyCb5wmNgA3cY7wfe1cB1w2GIvwo4aMB3xhpYCRpryCmBOf0NBhwadEtjyGovSs6ZpJs6GPE0hUjWMCIE39VfW3JWX3VDVtAA5hFhTfQbjWWi0bE/LPFboKskX9kZmpE1yV82QJaQmO8qkHc98GaCc6lb6/0MmLJdbhc976ZjtFBMQCpPmeDqCQ+Ebc3eBm7AcOp0yVECKigPZtfBry3PBz5kR59c4gu4Grp1Wo5ja6WeN1zgsCBpzNeqASTOBu4Mm5+2NIKbObZmjMJxmR09qrURsAQZJcqhCeAvX/xUDlR6cKG8gqQhkuBjwXh0nyuLG9T66qWNbQvVXQl7hSdyzRBwsJlJwe9hxhlq6V2lPAW1NdTCggijqxS7EeoJiE8fQEiKVLg0A2sax2qZ6S6hEuYbEqk1d8h7xATfpjEoWw8LtZQA2Hf6yylYgNEDfA1SHPCVfMLYEOdg9XclD72cbUmFg57BiKH+qhBdsgMbhiDtZPFayGIZur7hNpNUfFA198D1lksIoqjhGxyJw8li+FILGou1GCxoM1jQg8FGTxsM0vhSZaSScRUD1a0Jeyq/q/aF65vuj8rQ1b7aOeSdFHWuzlL9H0b+sVPOO/byiVpTiK1MmPGtiKDGB0nECmQNOsEdFfv7LebPMQGMSPpQV6LNJ2aFO06PCSWnh1OPF9urejtmUjVP2nICK+4sOdoGDTlHghSbPokz/pk4Ez6LM+E75MzIedzXp3LGlhOejTPBmTjzLMq8Q8Y4oc2Y56YZW5Cdr/rjTNjCGV1MZRuSvqjys0rIJYnqcmZoDQqqOvwKu+Z0LAugs7ArYK1lf/VetQB5Y0VfcSgYVkz8RgnjtLG7j5pv/A+XMDHJkuPSTnemeTM5xCowxpaAUzOIXaeMLDn9JZDJf2a8DjNyKryQGZaYMzIjr3seo0aWkI1qRlvBDlNBont1uD/1xVhyRvUYWQC74xmVlKuszGApK8n6szVcJO08vQvtqu7sLrgkZvKkp1xuxaff/Bp1W5jk9pDJHafzxD/1rscJOk782i3Pu7uUGbV/L9RuGM5zOjtt93dniLQlo5vbjqizo+jJsLPC9FWibnRh1VDj8KTAc0bBhd+Dn9quzv6u2u6MvfLOVAtciHyEZIc0SgRP+TZjh+b4GavpNxqoDRq03Z/2FKzYLe/x9Sla/hniXf8B&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs><style type="text/css">@import url(https://fonts.googleapis.com/css?family=Architects+Daughter);&#xa;</style></defs><g><rect x="180" y="174" width="300" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 298px; height: 1px; padding-top: 189px; margin-left: 181px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">For random reads, JuiceFS Client will prefetch the object storage data block</font></div></div></div></foreignObject><text x="330" y="193" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">For random reads, JuiceFS Client will prefetch the...</text></switch></g><rect x="0" y="74" width="600" height="60" fill="#ffe6cc" stroke="#d79b00" pointer-events="all"/><path d="M 150 74 L 150 134" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 450 74 L 450 134" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 300 74 L 300 134" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><rect x="225" y="89" width="150" height="30" fill="#ffe6cc" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 104px; margin-left: 226px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;"><font face="Courier New">file</font></span></div></div></div></foreignObject><text x="300" y="108" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">file</text></switch></g><path d="M 60 75 L 60 135" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 81 75 L 81 135" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 80 44 L 75 44 Q 70 44 70 49 L 70 51.5 Q 70 54 65 54 L 62.5 54 Q 60 54 65 54 L 67.5 54 Q 70 54 70 59 L 70 61.5 Q 70 64 75 64 L 80 64" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="rotate(90,70,54)" pointer-events="all"/><rect x="40" y="4" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 19px; margin-left: 41px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 16px;">random read</font></div></div></div></foreignObject><text x="70" y="23" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">random read</text></switch></g><path d="M 84.25 81 L 79.25 81 Q 74.25 81 74.25 91 L 74.25 144.25 Q 74.25 154.25 69.25 154.25 L 66.75 154.25 Q 64.25 154.25 69.25 154.25 L 71.75 154.25 Q 74.25 154.25 74.25 164.25 L 74.25 217.5 Q 74.25 227.5 79.25 227.5 L 84.25 227.5" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="translate(74.25,0)scale(-1,1)translate(-74.25,0)rotate(-90,74.25,154.25)" pointer-events="all"/><rect x="44.25" y="174" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 189px; margin-left: 45px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 16px;">prefetch<br />asynchronously<br /></span></div></div></div></foreignObject><text x="74" y="193" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">prefetch...</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>