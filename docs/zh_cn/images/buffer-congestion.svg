<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="656px" height="271px" viewBox="-0.5 -0.5 656 271" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2023-08-23T04:07:37.983Z&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/21.6.8 Chrome/114.0.5735.289 Electron/25.5.0 Safari/537.36&quot; version=&quot;21.6.8&quot; etag=&quot;DAy65xeVdSvbVPBmcaGN&quot; type=&quot;device&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;-ihzAo6XUtY36-2feLxR&quot;&gt;5Vpdc+I2FP01nmkfmrEtbOCRQNJ2pp3uTKbT7aOwBaiVLSrLC/TX9wpLNpZFgawhCfuQIF19WDrn6si6soem2fZHgderX3lKmBf66dZDMy8MhzGC/8qwqwxRNK4MS0HTyhQ0hhf6L9FGX1tLmpKiVVFyziRdt40Jz3OSyJYNC8E37WoLztpPXeMl6RheEsy61j9oKleVdRT5jf0nQpcr8+TA1yUZNpW1oVjhlG8OTOjJQ1PBuaxS2XZKmMLO4FK1ez5SWg9MkFye0yCsGnzBrNRz0+OSOzNZwcs8Jap+4KHHzYpK8rLGiSrdALtgW8mM6eIFZWzKGReQz3kOlR5TXKzq5oUU/O8aNFRbTBsvRI/jJ38SQYkeGhGSbI9OL6hBA2cjPCNS7KCKbhCONM7a0cKBzm8a2mrb6oCy2oi1qyzrvhs0IaEBdYOLLgHXvwxcAGoxSkiSuCCcj6JB5PcE4diCcNSF0IVgHwAOHADGTCooOIz+EMn4n5Kbgh+KvWJMoALMdLvHwZRDaql+J+s1owmWlOemTxhN1W1Vo8MVrNW1SpYZmyRSYf2okIVe2C94TtgnXtB9f2g251Ly7KDChNGlKpDc4pSXktEcyDNK1RNtAWrThlCXNuSgLe6BtqhnUWkpiENhbiwqMTotKkF4LVGJj66JYo3zr1oTv83/Uj4Y+i/g3WoLbBZG1feRhQFQyjZjbfw1TQ7msF4WCUBPhGO9ZDRN1WOcLtIWzz6Yja1FM4g6zMYOYlEPvA6vx+u8XCwA3W+NTTS01mnUXafXYnN0D1s/it5u6x/fA4CD4O0ANI/+4Ajaa/iWCAb3gKD9/o4cbytXQ/Ci4+V7RdCWwZsieBdnSFsHb4qg6xD58RAcviGCF53n3iuCtg4O/Bsi6Dq0fTgEbR28KYKu49GHQ9DWwZsieBdHElsHb4rgGWcSEyNcMLKdqJsGmDbJU52cJQwXBU3aQLZRByDE7vNh5k+VeYhMdrY9LJztTG5L5Wfdo0oftIJc00hlTJvXUkhS636k4KVItEmDIrFYEo1+dDbNBzRGDhqNTRCGJf3SHoSLW/2ET5zu48XHvCiw3KOaj27VeEinoyi0tmU7fFqh0Olo72r1tM/yPvOkS4Lh3VDQpfFzFByJKXnPvjd+9mBTHU2PBtAr81yctpwb4wriI+OZ8nxJCiuc345z/e8gX4FhdxanbhDuJrQWDU6/jl4rtBaecSy/dxmGuZjrjjh+8MMhGg8B8P1vVawvzYP99Yq5GYkfomEwCEfm/3FBfzdSHdgHn2j8MApfp9b2bbCrrx4F2xX8eLXYfLVA/gzCkReKCKBOgEac0MlvWN2CTsjsdtdAzo8Gerrz/r2g+VKND/6Y8nP4NXdDMDeYGthpBginkOLmNrAwt4H+HOdphUHoJ7gsSOHtP7Kpd14fKjT+5S8wZaVQqe8kzQgvVXcERFgU31/Rq1KywOW+8/fmWNa2Wb+HHmrpsB/PgmzzGVElX823WOjpPw==&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs><style type="text/css">@import url(https://fonts.googleapis.com/css?family=Architects+Daughter);&#xa;</style></defs><g><rect x="184" y="1" width="240" height="240" rx="36" ry="36" fill="none" stroke="#b9e0a5" stroke-width="3" stroke-dasharray="9 9" pointer-events="all"/><rect x="194" y="41" width="40" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="all"/><ellipse cx="49" cy="98.5" rx="7.5" ry="7.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><path d="M 49 106 L 49 131 M 49 111 L 34 111 M 49 111 L 64 111 M 49 131 L 34 151 M 49 131 L 64 151" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 158px; margin-left: 49px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;"><font style="font-size: 20px;">Application</font></div></div></div></foreignObject><text x="49" y="170" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Appli...</text></switch></g><rect x="534" y="1" width="120" height="240" rx="18" ry="18" fill="none" stroke="#b9e0a5" stroke-width="3" stroke-dasharray="9 9" pointer-events="all"/><rect x="564" y="106" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 121px; margin-left: 565px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;">Object Storage</span></div></div></div></foreignObject><text x="594" y="125" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Object Sto...</text></switch></g><rect x="274" y="11" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 26px; margin-left: 275px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px;">buffer</span></div></div></div></foreignObject><text x="304" y="30" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">buffer</text></switch></g><rect x="254" y="41" width="40" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="all"/><rect x="314" y="41" width="40" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="all"/><rect x="374" y="41" width="40" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="all"/><rect x="194" y="101" width="40" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="all"/><rect x="254" y="101" width="40" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="all"/><rect x="314" y="101" width="40" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="all"/><rect x="374" y="101" width="40" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="all"/><rect x="194" y="161" width="40" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="all"/><rect x="254" y="161" width="40" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="all"/><rect x="314" y="161" width="40" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="all"/><rect x="374" y="161" width="40" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="all"/><path d="M 424.5 126 L 424.5 116 L 514.5 116 L 514.5 105.5 L 533.5 121 L 514.5 136.5 L 514.5 126 Z" fill="#f8cecc" stroke="#b85450" stroke-miterlimit="10" pointer-events="all"/><rect x="444" y="101" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 116px; margin-left: 445px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style=""><font style="font-size: 31px;">🐌</font><br /><br /><span style="font-size: 16px;">Congestion</span></font><font style=""><br /></font></div></div></div></foreignObject><text x="474" y="120" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">🐌Congestion...</text></switch></g><path d="M 74.5 139.11 L 74.5 102.53 L 149.5 102.53 L 149.5 69.02 L 183.5 120.82 L 149.5 172.62 L 149.5 139.11 Z" fill="#f8cecc" stroke="#b85450" stroke-miterlimit="10" pointer-events="all"/><rect x="94" y="106" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 121px; margin-left: 95px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style=""><span style="font-size: 16px;">Intensive write</span><br /></font></div></div></div></foreignObject><text x="124" y="125" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Intensive...</text></switch></g><rect x="44" y="231" width="570" height="30" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 568px; height: 1px; padding-top: 246px; margin-left: 45px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px;">Using a large buffer with limited object storage bandwidth causes congestion and write failure (timeout errors)</font></div></div></div></foreignObject><text x="329" y="250" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Using a large buffer with limited object storage bandwidth causes congestion and write failure...</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>