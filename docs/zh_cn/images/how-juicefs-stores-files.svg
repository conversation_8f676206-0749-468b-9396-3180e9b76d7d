<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="2299px" height="1166px" viewBox="-0.5 -0.5 2299 1166" style="background-color: rgb(255, 255, 255);"><defs><style type="text/css">@import url(https://fonts.googleapis.com/css?family=Permanent+Marker);&#xa;</style></defs><g><rect x="2" y="2" width="1080" height="1160" fill="none" stroke="#cccccc" stroke-width="6" stroke-dasharray="18 18" pointer-events="all"/><rect x="1215" y="2" width="1080" height="1160" fill="none" stroke="#cccccc" stroke-width="6" stroke-dasharray="18 18" pointer-events="all"/><rect x="1375" y="342" width="820" height="100" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(2)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 408px; height: 1px; padding-top: 178px; margin-left: 690px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 30px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">/chunks/0/0/310_5_3431768</div></div></div></foreignObject><text x="690" y="208" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="30px">/chunks/0/0/310_5_3431768</text></switch></g><rect x="1375" y="462" width="820" height="100" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(2)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 408px; height: 1px; padding-top: 238px; margin-left: 690px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 30px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">/chunks/0/0/621_3_4234797</div></div></div></foreignObject><text x="690" y="268" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="30px">/chunks/0/0/621_3_4234797</text></switch></g><rect x="1375" y="522" width="760" height="100" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(2)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 378px; height: 1px; padding-top: 286px; margin-left: 689px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 70px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">...</div></div></div></foreignObject><text x="878" y="307" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="70px" text-anchor="middle">...</text></switch></g><rect x="1375" y="662" width="820" height="100" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(2)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 408px; height: 1px; padding-top: 338px; margin-left: 690px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 30px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">/chunks/123/4/888_2_xxxxxxx</div></div></div></foreignObject><text x="690" y="368" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="30px">/chunks/123/4/888_2_xxxxxxx</text></switch></g><path d="M 460.5 437 L 525 342" fill="none" stroke="#4d4d4d" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><rect x="375" y="427" width="95" height="100" fill="none" stroke="none" pointer-events="all"/><path d="M 375 438.52 C 375.71 432.44 380.46 427.66 386.45 427 L 450.28 427 L 470 446.9 L 470 514.58 C 469.66 521.23 464.46 526.57 457.9 527 L 386.45 527 C 380.55 526.36 375.83 521.73 375 515.77 Z M 382.77 513.97 C 382.77 516.37 384.43 518.44 386.75 518.92 L 458.05 518.92 C 460.33 518.4 461.94 516.34 461.93 513.97 L 461.93 451.55 L 446 451.55 L 446 435.18 L 386.75 435.18 C 384.62 435.71 383.08 437.6 382.97 439.83 Z M 399.9 459.02 L 443.91 459.02 L 443.91 467.3 L 399.9 467.3 Z M 399.9 474.78 L 443.91 474.78 L 443.91 483.06 L 399.9 483.06 Z M 399.9 490.54 L 443.91 490.54 L 443.91 498.92 L 399.9 498.92 Z" fill="#808080" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(2)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 271px; margin-left: 211px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">file1</div></div></div></foreignObject><text x="211" y="287" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">file1</text></switch></g><path d="M 455 249.69 C 455 245.44 458.13 242 462 242 L 476 242 C 479.87 242 483 245.44 483 249.69 L 588 249.69 C 591.87 249.69 595 253.14 595 257.38 L 595 334.31 C 595 338.56 591.87 342 588 342 L 462 342 C 458.13 342 455 338.56 455 334.31 Z M 455 257.38 L 595 257.38" fill="none" stroke="#0080f0" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)scale(2)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 146px; margin-left: 244px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Verdana; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">/</div></div></div></foreignObject><text x="263" y="151" fill="rgb(0, 0, 0)" font-family="Verdana" font-size="16px" text-anchor="middle">/</text></switch></g><path d="M 602.5 482 L 518.98 342" fill="none" stroke="#4d4d4d" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 555 493.52 C 555.71 487.44 560.46 482.66 566.45 482 L 630.28 482 L 650 501.9 L 650 569.58 C 649.66 576.23 644.46 581.57 637.9 582 L 566.45 582 C 560.55 581.36 555.83 576.73 555 570.77 Z M 562.77 568.97 C 562.77 571.37 564.43 573.44 566.75 573.92 L 638.05 573.92 C 640.33 573.4 641.94 571.34 641.93 568.97 L 641.93 506.55 L 626 506.55 L 626 490.18 L 566.75 490.18 C 564.62 490.71 563.08 492.6 562.97 494.83 Z M 579.9 514.02 L 623.91 514.02 L 623.91 522.3 L 579.9 522.3 Z M 579.9 529.78 L 623.91 529.78 L 623.91 538.06 L 579.9 538.06 Z M 579.9 545.54 L 623.91 545.54 L 623.91 553.92 L 579.9 553.92 Z" fill="#808080" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)scale(2)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 298px; margin-left: 301px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">file3</div></div></div></foreignObject><text x="301" y="314" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">file3</text></switch></g><path d="M 736.88 448.92 C 736.88 447.09 737.59 445.33 738.85 444.03 C 740.11 442.73 741.81 442 743.59 442 L 757.02 442 C 758.81 442 760.51 442.73 761.77 444.03 C 763.03 445.33 763.74 447.09 763.74 448.92 L 864.46 448.92 C 868.17 448.92 871.18 452.02 871.18 455.85 L 871.18 525.08 C 871.18 526.91 870.47 528.67 869.21 529.97 C 867.95 531.27 866.25 532 864.46 532 L 743.59 532 C 741.81 532 740.11 531.27 738.85 529.97 C 737.59 528.67 736.88 526.91 736.88 525.08 Z M 736.88 455.85 L 871.18 455.85" fill="none" stroke="#0080f0" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)scale(2)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 62px; height: 1px; padding-top: 244px; margin-left: 371px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Verdana; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">folder2</div></div></div></foreignObject><text x="402" y="248" fill="rgb(0, 0, 0)" font-family="Verdana" font-size="16px" text-anchor="middle">folder2</text></switch></g><path d="M 708.2 882 L 520.94 821.01" fill="none" stroke="#4d4d4d" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 755.7 916.33 L 1350.09 720.22" fill="none" stroke="#4d4d4d" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 1372.88 712.7 L 1347.5 737.92 L 1350.09 720.22 L 1337.47 707.53 Z" fill="#4d4d4d" stroke="#4d4d4d" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 660.7 893.52 C 661.41 887.44 666.16 882.66 672.15 882 L 735.98 882 L 755.7 901.9 L 755.7 969.58 C 755.36 976.23 750.16 981.57 743.6 982 L 672.15 982 C 666.25 981.36 661.53 976.73 660.7 970.77 Z M 668.47 968.97 C 668.47 971.37 670.13 973.44 672.45 973.92 L 743.75 973.92 C 746.03 973.4 747.64 971.34 747.63 968.97 L 747.63 906.55 L 731.7 906.55 L 731.7 890.18 L 672.45 890.18 C 670.32 890.71 668.78 892.6 668.67 894.83 Z M 685.6 914.02 L 729.61 914.02 L 729.61 922.3 L 685.6 922.3 Z M 685.6 929.78 L 729.61 929.78 L 729.61 938.06 L 685.6 938.06 Z M 685.6 945.54 L 729.61 945.54 L 729.61 953.92 L 685.6 953.92 Z" fill="#808080" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)scale(2)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 498px; margin-left: 354px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">file8</div></div></div></foreignObject><text x="354" y="514" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">file8</text></switch></g><path d="M 200.5 832 L 222.96 522" fill="none" stroke="#4d4d4d" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 115 833.52 C 115.71 827.44 120.46 822.66 126.45 822 L 190.28 822 L 210 841.9 L 210 909.58 C 209.66 916.23 204.46 921.57 197.9 922 L 126.45 922 C 120.55 921.36 115.83 916.73 115 910.77 Z M 122.77 908.97 C 122.77 911.37 124.43 913.44 126.75 913.92 L 198.05 913.92 C 200.33 913.4 201.94 911.34 201.93 908.97 L 201.93 846.55 L 186 846.55 L 186 830.18 L 126.75 830.18 C 124.62 830.71 123.08 832.6 122.97 834.83 Z M 139.9 854.02 L 183.91 854.02 L 183.91 862.3 L 139.9 862.3 Z M 139.9 869.78 L 183.91 869.78 L 183.91 878.06 L 139.9 878.06 Z M 139.9 885.54 L 183.91 885.54 L 183.91 893.92 L 139.9 893.92 Z" fill="#808080" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)scale(2)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 468px; margin-left: 81px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">file4</div></div></div></foreignObject><text x="81" y="484" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">file4</text></switch></g><path d="M 317.5 702 L 220.94 520.02" fill="none" stroke="#4d4d4d" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 270 713.52 C 270.71 707.44 275.46 702.66 281.45 702 L 345.28 702 L 365 721.9 L 365 789.58 C 364.66 796.23 359.46 801.57 352.9 802 L 281.45 802 C 275.55 801.36 270.83 796.73 270 790.77 Z M 277.77 788.97 C 277.77 791.37 279.43 793.44 281.75 793.92 L 353.05 793.92 C 355.33 793.4 356.94 791.34 356.93 788.97 L 356.93 726.55 L 341 726.55 L 341 710.18 L 281.75 710.18 C 279.62 710.71 278.08 712.6 277.97 714.83 Z M 294.9 734.02 L 338.91 734.02 L 338.91 742.3 L 294.9 742.3 Z M 294.9 749.78 L 338.91 749.78 L 338.91 758.06 L 294.9 758.06 Z M 294.9 765.54 L 338.91 765.54 L 338.91 773.92 L 294.9 773.92 Z" fill="#808080" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)scale(2)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 408px; margin-left: 159px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">file5</div></div></div></foreignObject><text x="159" y="424" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">file5</text></switch></g><path d="M 861.5 702 L 802.82 532" fill="none" stroke="#4d4d4d" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 990 582 L 1349.08 526.04" fill="none" stroke="#4d4d4d" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 1372.79 522.34 L 1343.64 543.08 L 1349.08 526.04 L 1338.71 511.46 Z" fill="#4d4d4d" stroke="#4d4d4d" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 814 713.52 C 814.71 707.44 819.46 702.66 825.45 702 L 889.28 702 L 909 721.9 L 909 789.58 C 908.66 796.23 903.46 801.57 896.9 802 L 825.45 802 C 819.55 801.36 814.83 796.73 814 790.77 Z M 821.77 788.97 C 821.77 791.37 823.43 793.44 825.75 793.92 L 897.05 793.92 C 899.33 793.4 900.94 791.34 900.93 788.97 L 900.93 726.55 L 885 726.55 L 885 710.18 L 825.75 710.18 C 823.62 710.71 822.08 712.6 821.97 714.83 Z M 838.9 734.02 L 882.91 734.02 L 882.91 742.3 L 838.9 742.3 Z M 838.9 749.78 L 882.91 749.78 L 882.91 758.06 L 838.9 758.06 Z M 838.9 765.54 L 882.91 765.54 L 882.91 773.92 L 838.9 773.92 Z" fill="#808080" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)scale(2)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 408px; margin-left: 431px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">file6</div></div></div></foreignObject><text x="431" y="424" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">file6</text></switch></g><path d="M 455 738.92 C 455 737.09 455.71 735.33 456.97 734.03 C 458.23 732.73 459.93 732 461.72 732 L 475.14 732 C 476.93 732 478.63 732.73 479.89 734.03 C 481.15 735.33 481.86 737.09 481.86 738.92 L 582.59 738.92 C 586.29 738.92 589.3 742.02 589.3 745.85 L 589.3 815.08 C 589.3 816.91 588.59 818.67 587.33 819.97 C 586.07 821.27 584.37 822 582.59 822 L 461.72 822 C 459.93 822 458.23 821.27 456.97 819.97 C 455.71 818.67 455 816.91 455 815.08 Z M 455 745.85 L 589.3 745.85" fill="none" stroke="#0080f0" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)scale(2)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 62px; height: 1px; padding-top: 389px; margin-left: 230px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Verdana; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">folder3</div></div></div></foreignObject><text x="261" y="393" fill="rgb(0, 0, 0)" font-family="Verdana" font-size="16px" text-anchor="middle">folder3</text></switch></g><path d="M 942.5 532 L 871.18 508.82" fill="none" stroke="#4d4d4d" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 990 582 L 1351.47 403.61" fill="none" stroke="#4d4d4d" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 1372.99 392.99 L 1351.38 421.5 L 1351.47 403.61 L 1337.22 392.8 Z" fill="#4d4d4d" stroke="#4d4d4d" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 895 543.52 C 895.71 537.44 900.46 532.66 906.45 532 L 970.28 532 L 990 551.9 L 990 619.58 C 989.66 626.23 984.46 631.57 977.9 632 L 906.45 632 C 900.55 631.36 895.83 626.73 895 620.77 Z M 902.77 618.97 C 902.77 621.37 904.43 623.44 906.75 623.92 L 978.05 623.92 C 980.33 623.4 981.94 621.34 981.93 618.97 L 981.93 556.55 L 966 556.55 L 966 540.18 L 906.75 540.18 C 904.62 540.71 903.08 542.6 902.97 544.83 Z M 919.9 564.02 L 963.91 564.02 L 963.91 572.3 L 919.9 572.3 Z M 919.9 579.78 L 963.91 579.78 L 963.91 588.06 L 919.9 588.06 Z M 919.9 595.54 L 963.91 595.54 L 963.91 603.92 L 919.9 603.92 Z" fill="#808080" stroke="none" pointer-events="none"/><g transform="translate(-0.5 -0.5)scale(2)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 323px; margin-left: 471px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">file7</div></div></div></foreignObject><text x="471" y="339" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">file7</text></switch></g><path d="M 228.6 432.99 L 518.98 342" fill="none" stroke="#4d4d4d" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 794.9 442.99 L 518.98 342" fill="none" stroke="#4d4d4d" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 520.94 741 L 220.94 520.02" fill="none" stroke="#4d4d4d" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 155 438.92 C 155 437.09 155.71 435.33 156.97 434.03 C 158.23 432.73 159.93 432 161.72 432 L 175.14 432 C 176.93 432 178.63 432.73 179.89 434.03 C 181.15 435.33 181.86 437.09 181.86 438.92 L 282.59 438.92 C 286.29 438.92 289.3 442.02 289.3 445.85 L 289.3 515.08 C 289.3 516.91 288.59 518.67 287.33 519.97 C 286.07 521.27 284.37 522 282.59 522 L 161.72 522 C 159.93 522 158.23 521.27 156.97 519.97 C 155.71 518.67 155 516.91 155 515.08 Z M 155 445.85 L 289.3 445.85" fill="none" stroke="#0080f0" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)scale(2)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 62px; height: 1px; padding-top: 239px; margin-left: 80px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Verdana; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">folder1</div></div></div></foreignObject><text x="111" y="243" fill="rgb(0, 0, 0)" font-family="Verdana" font-size="16px" text-anchor="middle">folder1</text></switch></g><g transform="translate(-0.5 -0.5)scale(2)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 271px; height: 1px; padding-top: 50px; margin-left: 742px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 30px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Object Storage</div></div></div></foreignObject><text x="878" y="59" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="30px" text-anchor="middle">Object Storage</text></switch></g><g transform="translate(-0.5 -0.5)scale(2)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 271px; height: 1px; padding-top: 50px; margin-left: 127px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 30px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Metadata Engine</div></div></div></foreignObject><text x="263" y="59" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="30px" text-anchor="middle">Metadata Engine</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>