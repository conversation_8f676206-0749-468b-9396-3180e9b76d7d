---
slug: /comparison/juicefs_vs_s3fs
---

# JuiceFS 对比 S3FS

[S3FS](https://github.com/s3fs-fuse/s3fs-fuse) 是一个 C++ 开发的开源工具，可以将 S3 对象存储通过 FUSE 挂载到本地，像本地磁盘一样进行读写访问。除了 Amazon S3，它还支持所有兼容 S3 API 的对象存储。

在基本功能方面，S3FS 与 JuiceFS 都能通过 FUSE 将对象存储 Bucket 挂载到本地并以 POSIX 接口使用。但在功能细节和技术实现上，二者有着本质的不同。

## 产品定位

S3FS 是一种实用工具，可以方便地将对象存储 Bucket 挂载到本地，以用户熟悉的方式进行读写，面向那些对性能和网络延迟不敏感的一般使用场景。

JuiceFS 是分布式文件系统，具有独特的数据管理方式以及一系列针对高性能、可靠性和安全性等方面的技术优化，主要解决海量数据的存储需求。

## 系统架构

S3FS 没有针对文件做特别的优化处理，它就像一个本地与对象存储之间的访问通道，本地挂载点看到的内容与对象存储浏览器上看到的一致，这样可以很方便地实现在本地使用云端存储。但从另一个角来看，正是因为这种简单的架构，使得 S3FS 对文件的检索和读写都需要与对象存储直接交互，网络延迟对性能和用户体验都会有较大的影响。

JuiceFS 采用数据和元数据分离的技术架构，任何文件都会先按照特定规则拆分成数据块再上传到对象存储，相应的元数据会存储在独立的数据库中。这样带来的好处是对文件的检索以及文件名等元信息的修改可以直接与响应速度更快的数据库交互，避开了与对象存储交互的网络延迟影响。

另外，在大文件的处理方面，虽然 S3FS 可以通过分块上传解决大文件的传输问题，但对象存储的特性决定了追加和改写文件需要重写整个对象。对于几十几百 GB 甚至 TB 级的大文件来说，重复上传势必会浪费大量的时间和带宽资源。

JuiceFS 则规避了此类问题，不论单个文件尺寸多大，在上传之前都会预先在本地按照特定规则拆分成数据块（默认 4MiB）。对任何文件的改写和追加最终都会变成生成新的数据块，而不是修改已生成的数据块，大大减少了时间和带宽资源的浪费。

有关 JuiceFS 的详细架构介绍请参考[文档](../../introduction/architecture.md)。

## 缓存机制

S3FS 支持磁盘缓存，但默认不启用。可以通过 `-o use_cache` 指定一个缓存路径来启用本地缓存。启用缓存后，任何文件的读写都会先写入缓存，然后再执行操作。S3FS 通过 MD5 来检测数据变化，确保数据正确性，同时降低文件的重复下载。由于 S3FS 涉及的所有操作都需要与 S3 交互，因此是否启用缓存对其应用体验有显著的影响。

S3FS 默认不限制缓存空间上限，对于较大的 Buket 可能导致缓存把磁盘写满，需要通过 `-o ensure_diskfree` 定义为磁盘保留的空间。另外，S3FS 没有缓存过期和清理机制，用户需要定期手动清理缓存，一旦缓存空间被存满，未缓存文件操作则需要直接与对象存储交互，处理大规模文件会有一定影响。

在缓存方面，JuiceFS 与 S3FS 完全不同，首先，JuiceFS 是保证数据一致性的。其次，JuiceFS 默认定义了 100GiB 的磁盘缓存使用上限，用户可以根据需要自由调整该值，而且默认会确保磁盘剩余空间低于 10% 时不再使用更多空间。当缓存用量达到上限，JuiceFS 会采用类似 LRU 的算法自动进行清理，确保后续的读写操作始终有缓存可用。

有关 JuiceFS 缓存的更多内容请参考[文档](../../guide/cache.md)。

## 功能特性

|                | S3FS                             | JuiceFS                                 |
|----------------|----------------------------------|-----------------------------------------|
| 数据存储       | S3                               | S3、其他对象存储、WebDAV、本地磁盘      |
| 元数据存储     | 无                               | 独立数据库                              |
| 系统           | Linux、macOS                     | Linux、macOS、Windows                   |
| 访问接口       | POSIX                            | POSIX、HDFS API、S3 Gateway、CSI Driver |
| POSIX 兼容     | 部分兼容                         | 完全兼容                                |
| 共享挂载       | 支持但不保证数据的完整性和一致性 | 保证强一致性                            |
| 本地缓存       | ✓                                | ✓                                       |
| 符号链接       | ✓                                | ✓                                       |
| 标准 Unix 权限 | ✓                                | ✓                                       |
| 强一致性       | ✕                                | ✓                                       |
| 扩展属性       | ✕                                | ✓                                       |
| 硬链接         | ✕                                | ✓                                       |
| 文件分块       | ✕                                | ✓                                       |
| 原子操作       | ✕                                | ✓                                       |
| 数据压缩       | ✕                                | ✓                                       |
| 客户端加密     | ✕                                | ✓                                       |
| 开发语言       | C++                              | Go                                      |
| 开源协议       | GPL v2.0                         | Apache License 2.0                      |

## 补充说明

[OSSFS](https://github.com/aliyun/ossfs)、[COSFS](https://github.com/tencentyun/cosfs)、[OBSFS](https://github.com/huaweicloud/huaweicloud-obs-obsfs) 等都是基于 S3FS 开发的衍生品，功能特性和用法与 S3FS 基本一致。
