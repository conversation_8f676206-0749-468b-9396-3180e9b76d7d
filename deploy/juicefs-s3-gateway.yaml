apiVersion: apps/v1
kind: Deployment
metadata:
  name: juicefs-s3-gateway
  namespace: kube-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: juicefs-s3-gateway
  template:
    metadata:
      labels:
        app.kubernetes.io/name: juicefs-s3-gateway
    spec:
      initContainers:
        - name: format
          image: juicedata/juicefs-csi-driver:v0.15.1
          command:
            - sh
            - -c
            - juicefs format --storage=${storage} --bucket=${bucket} --access-key=${accesskey} --secret-key=${secretkey} ${metaurl} ${name}
          envFrom:
            - secretRef:
                name: juicefs-secret
          env:
            - name: accesskey
              valueFrom:
                secretKeyRef:
                  name: juicefs-secret
                  key: access-key
            - name: secretkey
              valueFrom:
                secretKeyRef:
                  name: juicefs-secret
                  key: secret-key
      containers:
        - name: gateway
          image: juicedata/juicefs-csi-driver:v0.15.1
          command:
            - sh
            - -c
            - juicefs gateway ${METAURL} ${NODE_IP}:9000 --metrics=${NODE_IP}:9567
          env:
            - name: NODE_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: METAURL
              valueFrom:
                secretKeyRef:
                  name: juicefs-secret
                  key: metaurl
            - name: MINIO_ROOT_USER
              valueFrom:
                secretKeyRef:
                  name: juicefs-secret
                  key: access-key
            - name: MINIO_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: juicefs-secret
                  key: secret-key
          ports:
            - containerPort: 9000
            - containerPort: 9567
          resources:
            limits:
              cpu: 5000m
              memory: 5Gi
            requests:
              cpu: 1000m
              memory: 1Gi
---
apiVersion: v1
kind: Service
metadata:
  name: juicefs-s3-gateway
  namespace: kube-system
  labels:
    app.kubernetes.io/name: juicefs-s3-gateway
spec:
  selector:
    app.kubernetes.io/name: juicefs-s3-gateway
  ports:
    - name: http
      port: 9000
      targetPort: 9000
    - name: metrics
      port: 9567
      targetPort: 9567
