/*
 * JuiceFS, Copyright 2021 Juicedata, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cmd

import (
	"fmt"
	"os"
	"runtime"
	"strings"
	"testing"
	"time"

	"github.com/juicedata/juicefs/pkg/meta"
)

func TestWarmup(t *testing.T) {
	mountTemp(t, nil, nil, nil)
	defer umountTemp(t)

	if err := os.WriteFile(fmt.Sprintf("%s/f1.txt", testMountPoint), []byte("test"), 0644); err != nil {
		t.Fatalf("write file failed: %s", err)
	}
	m := meta.NewClient(testMeta, nil)
	format, err := m.Load(true)
	if err != nil {
		t.Fatalf("load setting err: %s", err)
	}
	uuid := format.UUID
	var cacheDir = "/var/jfsCache"
	var filePath string
	switch runtime.GOOS {
	case "linux":
		if os.Getuid() == 0 {
			break
		}
		fallthrough
	case "darwin", "windows":
		homeDir, err := os.UserHomeDir()
		if err != nil {
			t.Fatalf("%v", err)
		}
		cacheDir = fmt.Sprintf("%s/.juicefs/cache", homeDir)
	}

	os.RemoveAll(fmt.Sprintf("%s/%s", cacheDir, uuid))
	defer os.RemoveAll(fmt.Sprintf("%s/%s", cacheDir, uuid))

	if err = Main([]string{"", "warmup", testMountPoint}); err != nil {
		t.Fatalf("warmup: %s", err)
	}

	time.Sleep(2 * time.Second)
	filePath = fmt.Sprintf("%s/%s/raw/chunks/0/0/1_0_4", cacheDir, uuid)
	content, err := os.ReadFile(filePath)
	if err != nil || len(content) < 4 || string(content[:4]) != "test" {
		t.Fatalf("warmup: %s; got content %s", err, content)
	}
}

func TestSplitParamter(t *testing.T) {
	// s := []string{
	// 	"pai-extra-dataset-default-demo-test1=cacheDir:/var/jfsCache,cacheSize:1000",
	// 	"pai-extra-dataset-asd-demo-test1=cacheDir:/var/jfsCache,cacheSize:1000",
	// 	"pai-extra-dataset-asd-demo-test2=dir:",
	// 	"pai-extra-dataset-asd-demo-test3=dir:adsd/ef",
	// }
	// d := make(map[string]map[string]any)
	// for _, ele := range s {
	// 	parse(ele, d)
	// }
	// t.Log(d)

	// s := []string{"ro", "cacheSize=0", "cacheDir=/var/jfsCache"}
	// re := parseOriginOption(s)
	// t.Log(re)

	options := []string{}
	//options = append(options, "ro")
	var extraOptions = make(map[string]map[string]any)
	mountFlag := []string{"cache-size=0", "backup-meta=8h"}
	for _, flag := range mountFlag {
		if strings.HasPrefix(flag, "pai-extra-") {
			if err := parse(flag, extraOptions); err != nil {
				//return nil, status.Errorf(codes.InvalidArgument, "invalid format: %s", flag)
				t.Fatal(err)
			}
		} else {
			options = append(options, flag)
		}

	}
	originOptions := parseOriginOption(options)
	t.Log("---", originOptions)
	t.Log("******", extraOptions)
	volCtx := map[string]string{
		"subPath": "dataset-default-demote-test",
	}
	mountOptions := []string{}
	if path, ok := volCtx["subPath"]; ok {
		if otherOptions, existExtra := extraOptions[path]; existExtra {
			// the subPath need another mount parameters
			for k, v := range otherOptions {
				if _, exist := originOptions[k]; exist {
					//cover it
					originOptions[k] = v
					continue
				}
				value := strings.TrimSpace(v.(string))
				if value != "" {
					mountOptions = append(mountOptions, k+"="+value)
				} else {
					mountOptions = append(mountOptions, k)
				}
			}
		}
	}
	options = toOriginOption(originOptions)
	t.Log(options)
	t.Log("-------")
	mountOptions = append(mountOptions, options...)
	t.Log(mountOptions)
}

func parse(s string, result map[string]map[string]any) error {
	index := strings.Index(s, "pai-extra-")
	if index == -1 {
		return fmt.Errorf("string must start with 'pai-extra-'")
	}
	s = s[index+10:]
	index = strings.Index(s, "=")
	if index == -1 {
		return fmt.Errorf("spliter must use '='")
	}
	key := s[:index]
	s = s[index+1:]

	arry := strings.Split(s, ",")
	result[key] = make(map[string]any)
	for _, v := range arry {
		index = strings.Index(v, ":")
		if index == -1 {
			return fmt.Errorf("invalid format")
		}
		result[key][v[:index]] = v[index+1:]
	}
	return nil
}

func parseOriginOption(opts []string) map[string]any {
	var os = make(map[string]any)
	for _, option := range opts {
		index := strings.Index(option, "=")
		if index == -1 {
			os[option] = ""
			continue
		}
		os[option[:index]] = option[index+1:]
	}
	return os
}
func toOriginOption(originOptions map[string]any) []string {
	var options = make([]string, 0)
	for k, v := range originOptions {
		value := strings.TrimSpace(v.(string))
		if value == "" {
			options = append(options, k)
		} else {
			options = append(options, k+"="+value)
		}
	}
	return options
}
