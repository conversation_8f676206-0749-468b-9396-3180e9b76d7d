/*
 * JuiceFS, Copyright 2021 Juicedata, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package utils

import "golang.org/x/sys/windows"

type Rusage struct {
	kernel windows.Filetime
	user   windows.Filetime
}

func (ru *Rusage) GetUtime() float64 {
	return float64((int64(ru.user.HighDateTime)<<32)+int64(ru.user.LowDateTime)) / 10 / 1e6
}

func (ru *Rusage) GetStime() float64 {
	return float64((int64(ru.kernel.HighDateTime)<<32)+int64(ru.kernel.LowDateTime)) / 10 / 1e6
}

func GetRusage() *Rusage {
	h := windows.CurrentProcess()
	var creation, exit, kernel, user windows.Filetime
	err := windows.GetProcessTimes(h, &creation, &exit, &kernel, &user)
	if err == nil {
		return &Rusage{kernel, user}
	}
	return nil
}
