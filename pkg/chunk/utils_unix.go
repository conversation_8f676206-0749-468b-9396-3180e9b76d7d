//go:build !windows
// +build !windows

/*
 * JuiceFS, Copyright 2020 Juicedata, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package chunk

import (
	"os"
	"syscall"
)

func getNlink(fi os.FileInfo) int {
	if sst, ok := fi.Sys().(*syscall.Stat_t); ok {
		return int(sst.Nlink)
	}
	return 1
}

func getDiskUsage(path string) (uint64, uint64, uint64, uint64) {
	var stat syscall.Statfs_t
	if err := syscall.Statfs(path, &stat); err == nil {
		return stat.Blocks * uint64(stat.Bsize), stat.Bavail * uint64(stat.Bsize), stat.Files, stat.Ffree
	} else {
		logger.Warnf("statfs %s: %s", path, err)
		return 1, 1, 1, 1
	}
}

func changeMode(dir string, st os.FileInfo, mode os.FileMode) {
	sst := st.Sys().(*syscall.Stat_t)
	if os.Getuid() == int(sst.Uid) {
		_ = os.Chmod(dir, mode)
	}
}
